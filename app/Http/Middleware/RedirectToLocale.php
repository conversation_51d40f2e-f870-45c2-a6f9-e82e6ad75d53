<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class RedirectToLocale
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // If we're at the root URL and the locale is not set in the session
        if ($request->path() === '/' && !$request->session()->has('locale')) {
            // Set the locale to Czech for the current request
            app()->setLocale('cs');
            
            // But redirect to the English version for subsequent requests
            return redirect('/en');
        }

        return $next($request);
    }
}

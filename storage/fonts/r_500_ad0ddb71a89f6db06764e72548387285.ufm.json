{"codeToName": {"13": "CR", "32": "space", "33": "exclam", "34": "quotedbl", "35": "numbersign", "36": "dollar", "37": "percent", "38": "ampersand", "39": "<PERSON><PERSON><PERSON>", "40": "parenleft", "41": "parenright", "42": "asterisk", "43": "plus", "44": "comma", "45": "hyphen", "46": "period", "47": "slash", "48": "zero", "49": "one", "50": "two", "51": "three", "52": "four", "53": "five", "54": "six", "55": "seven", "56": "eight", "57": "nine", "58": "colon", "59": "semicolon", "60": "less", "61": "equal", "62": "greater", "63": "question", "64": "at", "65": "A", "66": "B", "67": "C", "68": "D", "69": "E", "70": "F", "71": "G", "72": "H", "73": "I", "74": "J", "75": "K", "76": "L", "77": "M", "78": "N", "79": "O", "80": "P", "81": "Q", "82": "R", "83": "S", "84": "T", "85": "U", "86": "V", "87": "W", "88": "X", "89": "Y", "90": "Z", "91": "bracketleft", "92": "backslash", "93": "bracketright", "94": "asciicircum", "95": "underscore", "96": "grave", "97": "a", "98": "b", "99": "c", "100": "d", "101": "e", "102": "f", "103": "g", "104": "h", "105": "i", "106": "j", "107": "k", "108": "l", "109": "m", "110": "n", "111": "o", "112": "p", "113": "q", "114": "r", "115": "s", "116": "t", "117": "u", "118": "v", "119": "w", "120": "x", "121": "y", "122": "z", "123": "braceleft", "124": "bar", "125": "braceright", "126": "asciitilde", "160": "uni00A0", "161": "exclamdown", "162": "cent", "163": "sterling", "164": "currency", "165": "yen", "166": "brokenbar", "167": "section", "168": "<PERSON><PERSON><PERSON>", "169": "copyright", "170": "ordfeminine", "171": "guillemotleft", "172": "logicalnot", "174": "registered", "175": "macron", "176": "degree", "177": "plus<PERSON>us", "178": "uni00B2", "179": "uni00B3", "180": "acute", "181": "uni00B5", "182": "paragraph", "183": "periodcentered", "184": "cedilla", "185": "uni00B9", "186": "ordmasculine", "187": "guil<PERSON><PERSON><PERSON>", "188": "onequarter", "189": "onehalf", "190": "threequarters", "191": "questiondown", "192": "<PERSON><PERSON>", "193": "Aacute", "194": "Acircumflex", "195": "<PERSON><PERSON>", "196": "Adieresis", "197": "<PERSON><PERSON>", "198": "AE", "199": "Ccedilla", "200": "<PERSON><PERSON>", "201": "Eacute", "202": "Ecircumflex", "203": "Edieresis", "204": "<PERSON><PERSON>", "205": "Iacute", "206": "Icircumflex", "207": "Idieresis", "208": "Eth", "209": "Ntilde", "210": "<PERSON><PERSON>", "211": "Oacute", "212": "Ocircumflex", "213": "<PERSON><PERSON><PERSON>", "214": "Odieresis", "215": "multiply", "216": "<PERSON><PERSON><PERSON>", "217": "<PERSON><PERSON>", "218": "Uacute", "219": "Ucircumflex", "220": "Udieresis", "221": "Ya<PERSON>", "222": "Thorn", "223": "germandbls", "224": "agrave", "225": "aacute", "226": "acircumflex", "227": "atilde", "228": "adieresis", "229": "aring", "230": "ae", "231": "ccedilla", "232": "egrave", "233": "eacute", "234": "ecircumflex", "235": "edieresis", "236": "igrave", "237": "iacute", "238": "icircumflex", "239": "idieresis", "240": "eth", "241": "ntilde", "242": "ograve", "243": "oacute", "244": "ocircumflex", "245": "otilde", "246": "odieresis", "247": "divide", "248": "oslash", "249": "ugrave", "250": "uacute", "251": "ucircumflex", "252": "udieresis", "253": "yacute", "254": "thorn", "255": "ydieresis", "256": "Amacron", "257": "amacron", "258": "Abreve", "259": "abreve", "260": "Aogonek", "261": "aogonek", "262": "Cacute", "263": "cacute", "264": "Ccircumflex", "265": "ccircumflex", "266": "Cdotaccent", "267": "cdotaccent", "268": "<PERSON><PERSON><PERSON>", "269": "ccaron", "270": "<PERSON><PERSON><PERSON>", "271": "dcaron", "272": "Dcroat", "273": "dmacron", "274": "Emacron", "275": "emacron", "276": "Ebreve", "277": "ebreve", "278": "Edotaccent", "279": "edotaccent", "280": "Eogonek", "281": "eogonek", "282": "<PERSON><PERSON><PERSON>", "283": "ecaron", "284": "Gcircumflex", "285": "gcircumflex", "286": "Gbreve", "287": "gbreve", "288": "Gdotaccent", "289": "gdotaccent", "290": "uni0122", "291": "uni0123", "292": "Hcircumflex", "293": "hcircumflex", "294": "H<PERSON>", "295": "hbar", "296": "Itilde", "297": "itilde", "298": "Imacron", "299": "imacron", "300": "Ibreve", "301": "i<PERSON>ve", "302": "Iogonek", "303": "iogonek", "304": "Idot", "305": "dotlessi", "306": "IJ", "307": "ij", "308": "Jcircumflex", "309": "jcircumflex", "310": "uni0136", "311": "uni0137", "312": "kgreenlandic", "313": "<PERSON><PERSON>", "314": "lacute", "315": "uni013B", "316": "uni013C", "317": "<PERSON><PERSON><PERSON>", "318": "lcaron", "319": "Ldot", "320": "ldot", "321": "Lslash", "322": "lslash", "323": "Nacute", "324": "nacute", "325": "uni0145", "326": "uni0146", "327": "<PERSON><PERSON><PERSON>", "328": "ncaron", "329": "napostrophe", "330": "Eng", "331": "eng", "332": "Omacron", "333": "omacron", "334": "Obreve", "335": "obreve", "336": "Ohungarumlau<PERSON>", "337": "ohungarumlaut", "338": "OE", "339": "oe", "340": "<PERSON><PERSON>", "341": "racute", "342": "uni0156", "343": "uni0157", "344": "<PERSON><PERSON><PERSON>", "345": "rcaron", "346": "Sacute", "347": "sacute", "348": "Scircumflex", "349": "scircumflex", "350": "Scedilla", "351": "scedilla", "352": "<PERSON><PERSON><PERSON>", "353": "scaron", "354": "uni0162", "355": "uni0163", "356": "<PERSON><PERSON><PERSON>", "357": "tcaron", "358": "Tbar", "359": "tbar", "360": "Utilde", "361": "utilde", "362": "Umacron", "363": "umacron", "364": "Ubreve", "365": "ubreve", "366": "<PERSON><PERSON>", "367": "uring", "368": "Uhungaru<PERSON>lau<PERSON>", "369": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "370": "Uogonek", "371": "uogonek", "372": "Wcircumflex", "373": "wcircumflex", "374": "Ycircumflex", "375": "ycircumflex", "376": "Ydieresis", "377": "Zacute", "378": "zacute", "379": "Zdotaccent", "380": "zdotaccent", "381": "<PERSON><PERSON><PERSON>", "382": "z<PERSON>on", "383": "longs", "399": "uni018F", "402": "florin", "416": "<PERSON><PERSON>", "417": "ohorn", "431": "<PERSON><PERSON>", "432": "uhorn", "496": "uni01F0", "506": "Aringacute", "507": "aring<PERSON><PERSON>", "508": "AEacute", "509": "aeacute", "510": "Oslashacute", "511": "oslashacute", "536": "uni0218", "537": "uni0219", "538": "uni021A", "539": "uni021B", "567": "uni0237", "601": "uni0259", "700": "uni02BC", "710": "circumflex", "711": "caron", "713": "uni02C9", "728": "breve", "729": "dotaccent", "730": "ring", "731": "ogonek", "732": "tilde", "733": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "755": "uni02F3", "768": "gravecomb", "769": "acutecomb", "770": "uni0302", "771": "tildecomb", "772": "uni0304", "774": "uni0306", "775": "uni0307", "776": "uni0308", "777": "hookab<PERSON><PERSON>", "778": "uni030A", "779": "uni030B", "780": "uni030C", "783": "uni030F", "786": "uni0312", "795": "uni031B", "803": "dotbelowcomb", "805": "uni0325", "806": "uni0326", "807": "uni0327", "808": "uni0328", "821": "uni0335", "822": "uni0336", "823": "uni0337", "824": "uni0338", "900": "tonos", "901": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "902": "Alphatonos", "903": "anoteleia", "904": "Epsilontonos", "905": "Etatonos", "906": "Iotatonos", "908": "Omicrontonos", "910": "Upsilontonos", "911": "Omegatonos", "912": "iotadieresistonos", "913": "Alpha", "914": "Beta", "915": "Gamma", "916": "uni0394", "917": "Epsilon", "918": "Zeta", "919": "Eta", "920": "Theta", "921": "Iota", "922": "Kappa", "923": "Lambda", "924": "Mu", "925": "<PERSON>u", "926": "Xi", "927": "Omicron", "928": "Pi", "929": "Rho", "931": "Sigma", "932": "Tau", "933": "Upsilon", "934": "Phi", "935": "<PERSON>", "936": "Psi", "937": "uni03A9", "938": "Iotadieresis", "939": "Upsilondieresis", "940": "alphatonos", "941": "epsilontonos", "942": "etatonos", "943": "iotatonos", "944": "upsilondieresistonos", "945": "alpha", "946": "beta", "947": "gamma", "948": "delta", "949": "epsilon", "950": "zeta", "951": "eta", "952": "theta", "953": "iota", "954": "kappa", "955": "lambda", "956": "uni03BC", "957": "nu", "958": "xi", "959": "omicron", "960": "pi", "961": "rho", "962": "uni03C2", "963": "sigma", "964": "tau", "965": "upsilon", "966": "phi", "967": "chi", "968": "psi", "969": "omega", "970": "iotadieresis", "971": "upsilondieresis", "972": "omicrontonos", "973": "upsilontonos", "974": "omegatonos", "977": "uni03D1", "978": "uni03D2", "982": "uni03D6", "1024": "uni0400", "1025": "uni0401", "1026": "uni0402", "1027": "uni0403", "1028": "uni0404", "1029": "uni0405", "1030": "uni0406", "1031": "uni0407", "1032": "uni0408", "1033": "uni0409", "1034": "uni040A", "1035": "uni040B", "1036": "uni040C", "1037": "uni040D", "1038": "uni040E", "1039": "uni040F", "1040": "uni0410", "1041": "uni0411", "1042": "uni0412", "1043": "uni0413", "1044": "uni0414", "1045": "uni0415", "1046": "uni0416", "1047": "uni0417", "1048": "uni0418", "1049": "uni0419", "1050": "uni041A", "1051": "uni041B", "1052": "uni041C", "1053": "uni041D", "1054": "uni041E", "1055": "uni041F", "1056": "uni0420", "1057": "uni0421", "1058": "uni0422", "1059": "uni0423", "1060": "uni0424", "1061": "uni0425", "1062": "uni0426", "1063": "uni0427", "1064": "uni0428", "1065": "uni0429", "1066": "uni042A", "1067": "uni042B", "1068": "uni042C", "1069": "uni042D", "1070": "uni042E", "1071": "uni042F", "1072": "uni0430", "1073": "uni0431", "1074": "uni0432", "1075": "uni0433", "1076": "uni0434", "1077": "uni0435", "1078": "uni0436", "1079": "uni0437", "1080": "uni0438", "1081": "uni0439", "1082": "uni043A", "1083": "uni043B", "1084": "uni043C", "1085": "uni043D", "1086": "uni043E", "1087": "uni043F", "1088": "uni0440", "1089": "uni0441", "1090": "uni0442", "1091": "uni0443", "1092": "uni0444", "1093": "uni0445", "1094": "uni0446", "1095": "uni0447", "1096": "uni0448", "1097": "uni0449", "1098": "uni044A", "1099": "uni044B", "1100": "uni044C", "1101": "uni044D", "1102": "uni044E", "1103": "uni044F", "1104": "uni0450", "1105": "uni0451", "1106": "uni0452", "1107": "uni0453", "1108": "uni0454", "1109": "uni0455", "1110": "uni0456", "1111": "uni0457", "1112": "uni0458", "1113": "uni0459", "1114": "uni045A", "1115": "uni045B", "1116": "uni045C", "1117": "uni045D", "1118": "uni045E", "1119": "uni045F", "1120": "uni0460", "1121": "uni0461", "1122": "uni0462", "1123": "uni0463", "1124": "uni0464", "1125": "uni0465", "1126": "uni0466", "1127": "uni0467", "1128": "uni0468", "1129": "uni0469", "1130": "uni046A", "1131": "uni046B", "1132": "uni046C", "1133": "uni046D", "1134": "uni046E", "1135": "uni046F", "1136": "uni0470", "1137": "uni0471", "1138": "uni0472", "1139": "uni0473", "1140": "uni0474", "1141": "uni0475", "1142": "uni0476", "1143": "uni0477", "1144": "uni0478", "1145": "uni0479", "1146": "uni047A", "1147": "uni047B", "1148": "uni047C", "1149": "uni047D", "1150": "uni047E", "1151": "uni047F", "1152": "uni0480", "1153": "uni0481", "1154": "uni0482", "1155": "uni0483", "1156": "uni0484", "1157": "uni0485", "1158": "uni0486", "1160": "uni0488", "1161": "uni0489", "1162": "uni048A", "1163": "uni048B", "1164": "uni048C", "1165": "uni048D", "1166": "uni048E", "1167": "uni048F", "1168": "uni0490", "1169": "uni0491", "1170": "uni0492", "1171": "uni0493", "1172": "uni0494", "1173": "uni0495", "1174": "uni0496", "1175": "uni0497", "1176": "uni0498", "1177": "uni0499", "1178": "uni049A", "1179": "uni049B", "1180": "uni049C", "1181": "uni049D", "1182": "uni049E", "1183": "uni049F", "1184": "uni04A0", "1185": "uni04A1", "1186": "uni04A2", "1187": "uni04A3", "1188": "uni04A4", "1189": "uni04A5", "1190": "uni04A6", "1191": "uni04A7", "1192": "uni04A8", "1193": "uni04A9", "1194": "uni04AA", "1195": "uni04AB", "1196": "uni04AC", "1197": "uni04AD", "1198": "Ustraitcy", "1199": "ustraitcy", "1200": "Ustraitstrokecy", "1201": "ustraitstrokecy", "1202": "uni04B2", "1203": "uni04B3", "1204": "uni04B4", "1205": "uni04B5", "1206": "uni04B6", "1207": "uni04B7", "1208": "uni04B8", "1209": "uni04B9", "1210": "uni04BA", "1211": "uni04BB", "1212": "uni04BC", "1213": "uni04BD", "1214": "uni04BE", "1215": "uni04BF", "1216": "uni04C0", "1217": "uni04C1", "1218": "uni04C2", "1219": "uni04C3", "1220": "uni04C4", "1221": "uni04C5", "1222": "uni04C6", "1223": "uni04C7", "1224": "uni04C8", "1225": "uni04C9", "1226": "uni04CA", "1227": "uni04CB", "1228": "uni04CC", "1229": "uni04CD", "1230": "uni04CE", "1231": "uni04CF", "1232": "uni04D0", "1233": "uni04D1", "1234": "uni04D2", "1235": "uni04D3", "1236": "uni04D4", "1237": "uni04D5", "1238": "uni04D6", "1239": "uni04D7", "1240": "uni04D8", "1241": "uni04D9", "1242": "uni04DA", "1243": "uni04DB", "1244": "uni04DC", "1245": "uni04DD", "1246": "uni04DE", "1247": "uni04DF", "1248": "uni04E0", "1249": "uni04E1", "1250": "uni04E2", "1251": "uni04E3", "1252": "uni04E4", "1253": "uni04E5", "1254": "uni04E6", "1255": "uni04E7", "1256": "uni04E8", "1257": "uni04E9", "1258": "uni04EA", "1259": "uni04EB", "1260": "uni04EC", "1261": "uni04ED", "1262": "uni04EE", "1263": "uni04EF", "1264": "uni04F0", "1265": "uni04F1", "1266": "uni04F2", "1267": "uni04F3", "1268": "uni04F4", "1269": "uni04F5", "1270": "uni04F6", "1271": "uni04F7", "1272": "uni04F8", "1273": "uni04F9", "1274": "uni04FA", "1275": "uni04FB", "1276": "uni04FC", "1277": "uni04FD", "1278": "uni04FE", "1279": "uni04FF", "1280": "uni0500", "1281": "uni0501", "1282": "uni0502", "1283": "uni0503", "1284": "uni0504", "1285": "uni0505", "1286": "uni0506", "1287": "uni0507", "1288": "uni0508", "1289": "uni0509", "1290": "uni050A", "1291": "uni050B", "1292": "uni050C", "1293": "uni050D", "1294": "uni050E", "1295": "uni050F", "1296": "uni0510", "1297": "uni0511", "1298": "uni0512", "1299": "uni0513", "7680": "uni1E00", "7681": "uni1E01", "7742": "uni1E3E", "7743": "uni1E3F", "7808": "<PERSON><PERSON>", "7809": "wgrave", "7810": "Wacute", "7811": "wacute", "7812": "Wdieresis", "7813": "wdieresis", "7838": "uni1E9E", "7840": "uni1EA0", "7841": "uni1EA1", "7842": "uni1EA2", "7843": "uni1EA3", "7844": "uni1EA4", "7845": "uni1EA5", "7846": "uni1EA6", "7847": "uni1EA7", "7848": "uni1EA8", "7849": "uni1EA9", "7850": "uni1EAA", "7851": "uni1EAB", "7852": "uni1EAC", "7853": "uni1EAD", "7854": "uni1EAE", "7855": "uni1EAF", "7856": "uni1EB0", "7857": "uni1EB1", "7858": "uni1EB2", "7859": "uni1EB3", "7860": "uni1EB4", "7861": "uni1EB5", "7862": "uni1EB6", "7863": "uni1EB7", "7864": "uni1EB8", "7865": "uni1EB9", "7866": "uni1EBA", "7867": "uni1EBB", "7868": "uni1EBC", "7869": "uni1EBD", "7870": "uni1EBE", "7871": "uni1EBF", "7872": "uni1EC0", "7873": "uni1EC1", "7874": "uni1EC2", "7875": "uni1EC3", "7876": "uni1EC4", "7877": "uni1EC5", "7878": "uni1EC6", "7879": "uni1EC7", "7880": "uni1EC8", "7881": "uni1EC9", "7882": "uni1ECA", "7883": "uni1ECB", "7884": "uni1ECC", "7885": "uni1ECD", "7886": "uni1ECE", "7887": "uni1ECF", "7888": "uni1ED0", "7889": "uni1ED1", "7890": "uni1ED2", "7891": "uni1ED3", "7892": "uni1ED4", "7893": "uni1ED5", "7894": "uni1ED6", "7895": "uni1ED7", "7896": "uni1ED8", "7897": "uni1ED9", "7898": "uni1EDA", "7899": "uni1EDB", "7900": "uni1EDC", "7901": "uni1EDD", "7902": "uni1EDE", "7903": "uni1EDF", "7904": "uni1EE0", "7905": "uni1EE1", "7906": "uni1EE2", "7907": "uni1EE3", "7908": "uni1EE4", "7909": "uni1EE5", "7910": "uni1EE6", "7911": "uni1EE7", "7912": "uni1EE8", "7913": "uni1EE9", "7914": "uni1EEA", "7915": "uni1EEB", "7916": "uni1EEC", "7917": "uni1EED", "7918": "uni1EEE", "7919": "uni1EEF", "7920": "uni1EF0", "7921": "uni1EF1", "7922": "<PERSON><PERSON>", "7923": "ygrave", "7924": "uni1EF4", "7925": "uni1EF5", "7926": "uni1EF6", "7927": "uni1EF7", "7928": "uni1EF8", "7929": "uni1EF9", "8013": "uni1F4D", "8189": "uni1FFD", "8190": "uni1FFE", "8192": "uni2000", "8193": "uni2001", "8194": "uni2002", "8195": "uni2003", "8196": "uni2004", "8197": "uni2005", "8198": "uni2006", "8199": "uni2007", "8200": "uni2008", "8201": "uni2009", "8202": "uni200A", "8203": "uni200B", "8211": "endash", "8212": "emdash", "8213": "uni2015", "8215": "underscoredbl", "8216": "quoteleft", "8217": "quoteright", "8218": "quotesinglbase", "8219": "quotereversed", "8220": "quotedblleft", "8221": "<PERSON><PERSON><PERSON><PERSON>", "8222": "quotedblbase", "8224": "dagger", "8225": "daggerdbl", "8226": "bullet", "8229": "twodotenleader", "8230": "ellipsis", "8240": "perth<PERSON>and", "8242": "minute", "8243": "second", "8249": "guil<PERSON>lle<PERSON>", "8250": "guil<PERSON><PERSON><PERSON>", "8252": "exclamdbl", "8260": "fraction", "8308": "uni2074", "8319": "uni207F", "8355": "franc", "8356": "lira", "8359": "peseta", "8363": "dong", "8364": "Euro", "8453": "uni2105", "8467": "uni2113", "8470": "uni2116", "8482": "trademark", "8486": "uni03A9", "8494": "estimated", "8539": "oneeighth", "8540": "threeeighths", "8541": "fiveeighths", "8542": "seveneighths", "8706": "<PERSON><PERSON><PERSON>", "8710": "uni0394", "8719": "product", "8721": "summation", "8722": "minus", "8725": "uni2215", "8730": "radical", "8734": "infinity", "8747": "integral", "8776": "approxequal", "8800": "notequal", "8804": "lessequal", "8805": "greaterequal", "9674": "lozenge", "63171": "uni0326.1", "64257": "fi", "64258": "fl", "64259": "uniFB03", "64260": "uniFB04", "65279": "uniFEFF", "65532": "uniFFFC", "65533": "uniFFFD"}, "isUnicode": true, "EncodingScheme": "FontSpecific", "FontName": "Roboto Slab Light", "FullName": "Roboto Slab Light", "Version": "Version 2.002", "PostScriptName": "RobotoSlab-Light", "Weight": "Medium", "ItalicAngle": "0", "IsFixedPitch": "false", "UnderlineThickness": "49", "UnderlinePosition": "-73", "FontHeightOffset": "0", "Ascender": "1048", "Descender": "-271", "FontBBox": ["-102", "-271", "1262", "1048"], "StartCharMetrics": "1154", "C": {"13": 244, "32": 244, "33": 224, "34": 332, "35": 608, "36": 536, "37": 712, "38": 620, "39": 214, "40": 311, "41": 307, "42": 465, "43": 559, "44": 192, "45": 387, "46": 228, "47": 390, "48": 565, "49": 406, "50": 545, "51": 538, "52": 575, "53": 522, "54": 549, "55": 544, "56": 550, "57": 556, "58": 190, "59": 191, "60": 499, "61": 550, "62": 516, "63": 452, "64": 883, "65": 732, "66": 626, "67": 645, "68": 670, "69": 628, "70": 611, "71": 676, "72": 751, "73": 292, "74": 563, "75": 703, "76": 578, "77": 926, "78": 758, "79": 696, "80": 622, "81": 699, "82": 671, "83": 583, "84": 653, "85": 722, "86": 717, "87": 1006, "88": 708, "89": 705, "90": 590, "91": 268, "92": 396, "93": 257, "94": 413, "95": 562, "96": 197, "97": 548, "98": 544, "99": 525, "100": 591, "101": 523, "102": 353, "103": 564, "104": 632, "105": 307, "106": 266, "107": 593, "108": 300, "109": 966, "110": 644, "111": 557, "112": 575, "113": 555, "114": 409, "115": 496, "116": 354, "117": 597, "118": 571, "119": 881, "120": 584, "121": 580, "122": 516, "123": 340, "124": 208, "125": 340, "126": 672, "160": 244, "161": 224, "162": 531, "163": 568, "164": 713, "165": 675, "166": 226, "167": 593, "168": 284, "169": 779, "170": 428, "171": 449, "172": 543, "174": 779, "175": 366, "176": 365, "177": 528, "178": 398, "179": 409, "180": 197, "181": 633, "182": 520, "183": 243, "184": 229, "185": 247, "186": 450, "187": 447, "188": 728, "189": 714, "190": 815, "191": 421, "192": 732, "193": 732, "194": 732, "195": 732, "196": 732, "197": 732, "198": 1005, "199": 645, "200": 628, "201": 628, "202": 628, "203": 628, "204": 292, "205": 292, "206": 292, "207": 292, "208": 670, "209": 758, "210": 696, "211": 696, "212": 696, "213": 696, "214": 696, "215": 516, "216": 696, "217": 722, "218": 722, "219": 722, "220": 722, "221": 705, "222": 614, "223": 617, "224": 548, "225": 548, "226": 548, "227": 548, "228": 548, "229": 548, "230": 863, "231": 525, "232": 523, "233": 523, "234": 523, "235": 523, "236": 307, "237": 307, "238": 307, "239": 307, "240": 576, "241": 644, "242": 557, "243": 557, "244": 557, "245": 557, "246": 557, "247": 561, "248": 557, "249": 597, "250": 597, "251": 597, "252": 597, "253": 580, "254": 573, "255": 580, "256": 732, "257": 548, "258": 732, "259": 548, "260": 732, "261": 548, "262": 645, "263": 525, "264": 645, "265": 525, "266": 645, "267": 525, "268": 645, "269": 525, "270": 670, "271": 591, "272": 670, "273": 591, "274": 628, "275": 523, "276": 628, "277": 523, "278": 628, "279": 523, "280": 628, "281": 523, "282": 628, "283": 523, "284": 676, "285": 564, "286": 676, "287": 564, "288": 676, "289": 564, "290": 676, "291": 564, "292": 751, "293": 632, "294": 751, "295": 632, "296": 292, "297": 307, "298": 292, "299": 307, "300": 292, "301": 307, "302": 292, "303": 307, "304": 292, "305": 307, "306": 852, "307": 588, "308": 563, "309": 266, "310": 703, "311": 593, "312": 589, "313": 578, "314": 300, "315": 578, "316": 300, "317": 578, "318": 300, "319": 578, "320": 300, "321": 578, "322": 300, "323": 758, "324": 644, "325": 758, "326": 644, "327": 758, "328": 644, "329": 644, "330": 755, "331": 607, "332": 696, "333": 557, "334": 696, "335": 557, "336": 696, "337": 557, "338": 981, "339": 940, "340": 671, "341": 409, "342": 671, "343": 409, "344": 671, "345": 409, "346": 583, "347": 496, "348": 583, "349": 496, "350": 583, "351": 496, "352": 583, "353": 496, "354": 653, "355": 354, "356": 653, "357": 354, "358": 653, "359": 354, "360": 722, "361": 597, "362": 722, "363": 597, "364": 722, "365": 597, "366": 722, "367": 597, "368": 722, "369": 597, "370": 722, "371": 597, "372": 1006, "373": 881, "374": 705, "375": 580, "376": 705, "377": 590, "378": 516, "379": 590, "380": 516, "381": 590, "382": 516, "383": 349, "399": 681, "402": 378, "416": 696, "417": 568, "431": 793, "432": 620, "496": 266, "506": 732, "507": 548, "508": 1005, "509": 863, "510": 696, "511": 557, "536": 583, "537": 496, "538": 653, "539": 354, "567": 266, "601": 522, "700": 178, "710": 310, "711": 310, "713": 366, "728": 324, "729": 104, "730": 222, "731": 216, "732": 363, "733": 413, "755": 234, "768": 0, "769": 0, "770": 0, "771": 0, "772": 0, "774": 0, "775": 0, "776": 0, "777": 0, "778": 0, "779": 0, "780": 0, "783": 0, "786": 0, "795": 0, "803": 0, "805": 0, "806": 0, "807": 0, "808": 0, "821": 0, "822": 0, "823": 0, "824": 0, "900": 232, "901": 299, "902": 732, "903": 240, "904": 696, "905": 832, "906": 364, "908": 696, "910": 769, "911": 647, "912": 307, "913": 732, "914": 626, "915": 590, "916": 692, "917": 628, "918": 590, "919": 751, "920": 677, "921": 292, "922": 703, "923": 704, "924": 926, "925": 758, "926": 583, "927": 696, "928": 762, "929": 622, "931": 573, "932": 653, "933": 705, "934": 712, "935": 682, "936": 801, "937": 646, "938": 292, "939": 705, "940": 588, "941": 534, "942": 578, "943": 307, "944": 582, "945": 588, "946": 565, "947": 530, "948": 557, "949": 534, "950": 492, "951": 578, "952": 563, "953": 307, "954": 589, "955": 554, "956": 633, "957": 557, "958": 491, "959": 563, "960": 622, "961": 554, "962": 532, "963": 606, "964": 500, "965": 582, "966": 710, "967": 582, "968": 756, "969": 813, "970": 307, "971": 582, "972": 563, "973": 582, "974": 813, "977": 641, "978": 614, "982": 824, "1024": 628, "1025": 628, "1026": 761, "1027": 590, "1028": 622, "1029": 583, "1030": 287, "1031": 292, "1032": 565, "1033": 1085, "1034": 1111, "1035": 846, "1036": 689, "1037": 763, "1038": 683, "1039": 762, "1040": 732, "1041": 643, "1042": 631, "1043": 590, "1044": 754, "1045": 628, "1046": 991, "1047": 594, "1048": 763, "1049": 763, "1050": 689, "1051": 736, "1052": 926, "1053": 751, "1054": 717, "1055": 762, "1056": 623, "1057": 652, "1058": 653, "1059": 683, "1060": 745, "1061": 708, "1062": 771, "1063": 728, "1064": 985, "1065": 988, "1066": 811, "1067": 867, "1068": 645, "1069": 619, "1070": 948, "1071": 670, "1072": 548, "1073": 567, "1074": 610, "1075": 513, "1076": 624, "1077": 523, "1078": 863, "1079": 507, "1080": 670, "1081": 670, "1082": 608, "1083": 632, "1084": 831, "1085": 669, "1086": 563, "1087": 670, "1088": 584, "1089": 525, "1090": 552, "1091": 580, "1092": 713, "1093": 584, "1094": 653, "1095": 652, "1096": 906, "1097": 907, "1098": 690, "1099": 794, "1100": 582, "1101": 526, "1102": 847, "1103": 623, "1104": 523, "1105": 523, "1106": 608, "1107": 513, "1108": 538, "1109": 496, "1110": 312, "1111": 307, "1112": 277, "1113": 874, "1114": 920, "1115": 632, "1116": 608, "1117": 670, "1118": 580, "1119": 670, "1120": 858, "1121": 791, "1122": 645, "1123": 555, "1124": 866, "1125": 776, "1126": 699, "1127": 604, "1128": 960, "1129": 848, "1130": 901, "1131": 778, "1132": 1173, "1133": 1032, "1134": 508, "1135": 460, "1136": 801, "1137": 756, "1138": 677, "1139": 577, "1140": 677, "1141": 578, "1142": 677, "1143": 578, "1144": 1276, "1145": 1131, "1146": 708, "1147": 567, "1148": 848, "1149": 755, "1150": 857, "1151": 770, "1152": 612, "1153": 511, "1154": 544, "1155": 0, "1156": 0, "1157": 0, "1158": 0, "1160": 984, "1161": 945, "1162": 763, "1163": 670, "1164": 645, "1165": 554, "1166": 627, "1167": 584, "1168": 585, "1169": 512, "1170": 590, "1171": 513, "1172": 596, "1173": 543, "1174": 991, "1175": 863, "1176": 594, "1177": 507, "1178": 689, "1179": 608, "1180": 708, "1181": 649, "1182": 689, "1183": 593, "1184": 872, "1185": 764, "1186": 751, "1187": 669, "1188": 1004, "1189": 753, "1190": 1026, "1191": 888, "1192": 750, "1193": 615, "1194": 652, "1195": 525, "1196": 653, "1197": 552, "1198": 705, "1199": 530, "1200": 705, "1201": 530, "1202": 708, "1203": 584, "1204": 884, "1205": 738, "1206": 728, "1207": 652, "1208": 728, "1209": 651, "1210": 695, "1211": 632, "1212": 748, "1213": 627, "1214": 748, "1215": 627, "1216": 287, "1217": 991, "1218": 863, "1219": 682, "1220": 617, "1221": 736, "1222": 632, "1223": 756, "1224": 657, "1225": 751, "1226": 669, "1227": 728, "1228": 652, "1229": 926, "1230": 831, "1231": 287, "1232": 732, "1233": 548, "1234": 732, "1235": 548, "1236": 1005, "1237": 863, "1238": 628, "1239": 523, "1240": 681, "1241": 522, "1242": 681, "1243": 522, "1244": 991, "1245": 863, "1246": 594, "1247": 507, "1248": 542, "1249": 544, "1250": 763, "1251": 670, "1252": 763, "1253": 670, "1254": 717, "1255": 563, "1256": 677, "1257": 577, "1258": 677, "1259": 577, "1260": 619, "1261": 526, "1262": 683, "1263": 580, "1264": 683, "1265": 580, "1266": 683, "1267": 580, "1268": 728, "1269": 652, "1270": 590, "1271": 513, "1272": 867, "1273": 794, "1274": 590, "1275": 513, "1276": 708, "1277": 584, "1278": 708, "1279": 584, "1280": 642, "1281": 594, "1282": 813, "1283": 833, "1284": 774, "1285": 706, "1286": 541, "1287": 487, "1288": 962, "1289": 825, "1290": 984, "1291": 883, "1292": 609, "1293": 518, "1294": 714, "1295": 638, "1296": 628, "1297": 534, "1298": 736, "1299": 632, "7680": 732, "7681": 548, "7742": 926, "7743": 966, "7808": 1006, "7809": 881, "7810": 1006, "7811": 881, "7812": 1006, "7813": 881, "7838": 678, "7840": 732, "7841": 548, "7842": 732, "7843": 548, "7844": 732, "7845": 548, "7846": 732, "7847": 548, "7848": 732, "7849": 548, "7850": 732, "7851": 548, "7852": 732, "7853": 548, "7854": 732, "7855": 548, "7856": 732, "7857": 548, "7858": 732, "7859": 548, "7860": 732, "7861": 548, "7862": 732, "7863": 548, "7864": 628, "7865": 523, "7866": 628, "7867": 523, "7868": 628, "7869": 523, "7870": 628, "7871": 523, "7872": 628, "7873": 523, "7874": 628, "7875": 523, "7876": 628, "7877": 523, "7878": 628, "7879": 523, "7880": 292, "7881": 307, "7882": 292, "7883": 307, "7884": 696, "7885": 557, "7886": 696, "7887": 557, "7888": 696, "7889": 557, "7890": 696, "7891": 557, "7892": 696, "7893": 557, "7894": 696, "7895": 557, "7896": 696, "7897": 557, "7898": 696, "7899": 568, "7900": 696, "7901": 568, "7902": 696, "7903": 568, "7904": 696, "7905": 568, "7906": 696, "7907": 568, "7908": 722, "7909": 597, "7910": 722, "7911": 597, "7912": 793, "7913": 620, "7914": 793, "7915": 620, "7916": 793, "7917": 620, "7918": 793, "7919": 620, "7920": 793, "7921": 620, "7922": 705, "7923": 580, "7924": 705, "7925": 580, "7926": 705, "7927": 580, "7928": 705, "7929": 580, "8013": 696, "8189": 232, "8190": 125, "8192": 510, "8193": 1020, "8194": 510, "8195": 1020, "8196": 340, "8197": 255, "8198": 170, "8199": 562, "8200": 273, "8201": 204, "8202": 102, "8203": 0, "8211": 687, "8212": 803, "8213": 803, "8215": 505, "8216": 178, "8217": 178, "8218": 192, "8219": 197, "8220": 299, "8221": 301, "8222": 306, "8224": 549, "8225": 556, "8226": 328, "8229": 446, "8230": 633, "8240": 958, "8242": 214, "8243": 347, "8249": 281, "8250": 280, "8252": 447, "8260": 432, "8308": 443, "8319": 457, "8355": 611, "8356": 580, "8359": 824, "8363": 608, "8364": 513, "8453": 702, "8467": 444, "8470": 1114, "8482": 608, "8486": 646, "8494": 585, "8539": 812, "8540": 876, "8541": 898, "8542": 827, "8706": 572, "8710": 692, "8719": 733, "8721": 603, "8722": 558, "8725": 432, "8730": 572, "8734": 1032, "8747": 335, "8776": 571, "8800": 507, "8804": 513, "8805": 516, "9674": 506, "63171": 0, "64257": 590, "64258": 647, "64259": 944, "64260": 1000, "65279": 0, "65532": 1087, "65533": 966}, "CIDtoGID_Compressed": true, "CIDtoGID": "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", "_version_": 6}
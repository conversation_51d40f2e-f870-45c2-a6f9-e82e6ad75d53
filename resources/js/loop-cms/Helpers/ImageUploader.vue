<script setup>

import { ref } from "vue";
import draggable from "vuedraggable";


const props = defineProps({
    modelValue: {
        type: Object,
        required: true,
    },
});

const drag = ref(false);
const model = ref(props.modelValue);

function addImages(newFiles) {
    [...newFiles.target.files].map((file) => addImage(file));
}

function addImage(file) {
    model.value.push(
        {
            file: null,
            order: model.value.length,
            name: file.name,
            type: file.type,
            size: file.size,
            fileData: file,
            th: URL.createObjectURL(file),
        }
    );
}


function removeImage(index) {
    model.value.splice(index, 1);
}

</script>

<template>
    <div
        class="uk-placeholder uk-text-center"
        :class="drag > 0 ? 'dragging' : ''"
    >



        <draggable
            v-model="model"
            @start="drag=true"
            @end="drag=false"
            item-key="order"
            @change="$emit('update:modelValue', model)"
            uk-grid
            class="uk-grid-small uk-grid-match uk-grid"
        >
            <template #item="{element, index}">
                <div>
                    <div class="uk-card uk-card-default">
                        <div
                            class="uk-card-media-top uk-flex uk-flex-middle uk-flex-center uk-margin-auto"
                            style="height: 200px; width: 200px"
                        >
                            <img
                                style="max-height: 100%; max-height: 100%"
                                :src="element.file ? '/'+element.file : element.th"
                            >

                            <div class="uk-position-top-right">
                                <button
                                    class="uk-button uk-button-small uk-text-small uk-button-danger"
                                    @click.prevent="removeImage(index)"
                                >
                                        <span
                                            uk-icon="icon: trash; ratio: 0.7"
                                        />
                                </button>
                            </div>
                        </div>

                        <div>
                            <input
                                v-model="element.name"
                                class="uk-input uk-text-small"
                                name="title"
                                placeholder="Popisek obrázku..."
                                type="text"
                            >
                        </div>
                    </div>
                </div>
            </template>
        </draggable>


        <div class="uk-margin-large">
            <span uk-icon="icon: cloud-upload" />
            <div class="uk-text-middle uk-margin-small">
                Obrázky nahrajete přetažením, nebo zmáčknutím tlačítka ...
            </div>
            <div uk-form-custom>
                <input
                    multiple
                    type="file"
                    accept="image/*"
                    @change="addImages"
                >
                <div class="uk-button-default uk-button">
                    Vybrat obrázky
                </div>
            </div>
        </div>
    </div>
</template>


<style lang="scss" scoped>
.uk-placeholder {

    &.dragging {
        background: #fff;
        color: #2196F3;
        border: 1px dashed #2196F3;

        .file-input label {
            background: #2196F3;
            color: #fff;
        }
    }


}
</style>

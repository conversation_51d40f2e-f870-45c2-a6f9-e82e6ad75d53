<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class RestaurantDailyMenu extends Model
{
    use HasFactory;

    protected $casts = [
        'category_labels' => 'array',
    ];

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'start',
        'end',
        'year',
        'week',
        'category_labels',
    ];


    public static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            $model->created_by = $model->created_by ?? auth()->user()->id;
            $model->edited_by = $model->edited_by ?? auth()->user()->id;
        });

        static::updating(function ($model) {
            $model->edited_by = $model->edited_by ?? auth()->user()->id;
            $model->updated_at = now();
        });
    }


    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }


    public function editor()
    {
        return $this->belongsTo(User::class, 'edited_by');
    }


    public function items()
    {
        return $this->hasMany(RestaurantDailyMenuItem::class);
    }


    public function scopeFilter($query, array $filters)
    {
        $query->when($filters['search'] ?? null, function ($query, $search) {
            $query->where(function ($query) use ($search) {
                $query->where('name', 'like', '%' . $search . '%');
            });
        });
    }


}

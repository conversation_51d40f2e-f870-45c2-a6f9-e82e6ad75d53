@extends('front-end.layouts.app')

@section('title', __('navigation.pressroom'))

@section('content')

    <div class="news-background"
         uk-scrollspy="target: [uk-scrollspy-class]; cls: uk-animation-fade; delay: false;">
        @include('front-end.components.navbar', ['navMode' => 'black'])

        <div class="uk-section uk-section-large ">
            <div class="uk-container uk-container-large">
                <h2 class="uk-h1 uk-heading-small uk-text-uppercase uk-text-center@s">Pressroom</h2>

                @include('front-end.components.news-navigation')

                <div class="uk-width-1-2@s uk-margin-auto">
                    @foreach ($references as $reference)
                        <div
                            class="uk-grid uk-flex-middle"
                            uk-grid>

                            <div class="uk-width-small@s uk-width-1-4 uk-flex-last uk-margin-right"><a
                                    href="{{$reference['link']}}"> <img
                                        src="/images/news/reference-logo/{{$reference['logo-img']}}"
                                        class="uk-display-inline-block"
                                        style="max-width: 120px; max-height: 18px; width: auto; height: 100%"
                                        alt=""> </a></div>
                            <div class="uk-width-expand">
                                <div
                                    class="uk-text-meta">{{$reference['date']}}
                                </div>
                                <h3 class="uk-h5 uk-text-primary uk-margin-remove-top">
                                    <a
                                        href="{{$reference['link']}}"
                                        class="uk-link-reset">{{$reference['title']}}</a></h3>

                            </div>

                        </div>
                    @endforeach

                </div>
            </div>
        </div>
    </div>

    @include('front-end.components.footer')

@endsection


// 1. Your custom variables and variable overwrites.
// $global-link-color: #DA7D02;
$global-color: #444;

$global-link-color: #bd9f57 !default;
$global-link-hover-color: #000 !default;
// $global-muted-background: #f2f2f2;

$global-font-family: 'Work Sans', sans-serif;
$global-primary-background: #363636;

$base-heading-font-family: 'Ubuntu', sans-serif;

$global-font-size: 15px;

$base-body-font-weight: 400;
// 333D66
$base-heading-color: #bd9f57;
$button-primary-background: #0b1215;

$text-primary-color: $global-color;

$bhm-dark-gray: #656565;
$bhm-white: #f8f8f8;
$bhm-gold: #bd9f57;



@import "~uikit/src/scss/variables-theme.scss";
@import "~uikit/src/scss/mixins-theme.scss";
@import "~uikit/src/scss/uikit-theme.scss";

// Import custom components

@import "about";
@import "career";
//@import "people";
@import "people-modern";
@import "portfolio";
@import "news";


body {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

// Common utility classes
.bhm-border-rounded {
    border-radius: 10px;
    overflow: hidden;
}



.tm-header-overlay {
    position: absolute;
    left: 0;
    right: 0;

}

.uk-navbar-toggle {
    min-height: 32px;
    color: $bhm-white;
}

// Navigation mode styles
.nav-mode-black {
    .uk-navbar-container {
        //background-color: rgba(255, 255, 255, 0.9) !important;
        background-color: transparent !important;
    }

    .uk-navbar-toggle-icon {
        color: #000;
    }
}

.nav-mode-white {
    .uk-navbar-container {
        background-color: transparent !important;
    }

    .uk-navbar-toggle-icon {
        color: #fff;
    }
}

.card-body-blur {
    @media (min-width: 960px) {
        background-color: rgba(0,0,0,0.05);
        backdrop-filter: blur(4px);
        -webkit-backdrop-filter: blur(4px);
    }
}




.home-background {
    background-image: url('/images/home/<USER>');
    background-position: center top;

    @media (max-width: 959px) {
        background-image: url('/images/home/<USER>');
        background-size: 2500px;
    }

    @media (min-width: 960px) {
        background-size: 250vw;
    }
}




// Thanks to: https://www.antimath.info/css/sass-sqrt-function/
// Using sqrt to get the size of the map pin. You could do this manually,
// if your pin is a different shape.
@function sqrt($r) {
    $x0: 1;
    $x1: $x0;
    @for $i from 1 through 10 {
        $x1: $x0 - ($x0 * $x0 - abs($r)) / (2 * $x0);
        $x0: $x1;
    }
    @return $x1;
}

$pinWidth: 60px;
$pinHeightFactor: ((1 + sqrt(2))/2);
$pinHeight: $pinHeightFactor * $pinWidth;
$pinColor: #bd9f57;
$shadowOpacity: .5;
$shadow-size: 50px;
$pulseSize: 100px;

$simpleColor: #737687;


.pin-wrap {
    position: absolute;
    width: $pinWidth;
    height: $pinHeight;
    margin-top: -$pinHeight;
    margin-left: -$pinWidth/2;
    // transform-origin: 50% ($pinHeightFactor * 100%) 0;
    // border: 1px solid red;
}

// .pin::before {
//     position: absolute;
//     display: block;
//     box-sizing: border-box;
//     width: $pinWidth;
//     height: $pinWidth;
//     content: '';
//     transform: rotateZ(-45deg);
//     // border: 18px solid darken($pinColor, 10%);
//     background: $pinColor;
//     border-radius: 50% 50% 50% 0;
// }


.label {
    position: absolute;
    left: 29px;
    top: 8px;
    background: #234a8b;
    border-radius: 0 10px 10px 0;
    padding: 6px 10px 6px 14px;
    font-weight: bold;
    color: white;
    white-space: nowrap;
    /*z-index: -1*/
    font-family: $base-body-font-family;
    font-size: 12px;
    font-weight: normal;
}

.label-left {
    position: absolute;
    left: 20px;
    top: -5px;
    background: $simpleColor;
    border-radius: 8px;
    padding: 7px 12px;
    font-weight: bold;
    color: white;
    white-space: nowrap;
    transform: translate(-100%, -100%);
    font-family: $base-body-font-family;
    font-size: 12px;
    font-weight: normal;

}

.pin.pin-poi::before {
    background: #737687;
}

.shadow {
    position: absolute;
}

.shadow::after {
    position: absolute;
    left: -100px - $shadow-size/2;
    display: block;
    width: $shadow-size;
    height: $shadow-size;
    margin-top: -$shadow-size/2;
    content: '';
    transform: rotateX(55deg);
    border-radius: 50%;
    box-shadow: rgba(0, 0, 0, $shadowOpacity) 100px 0 20px;
}

.pulse {
    position: absolute;
    margin-top: -$pulseSize/2;
    margin-left: -$pulseSize/2;
    transform: rotateX(55deg);
}

.pulse::after {
    display: block;
    width: $pulseSize;
    height: $pulseSize;
    content: '';
    animation: pulsate 1s ease-out;
    animation-delay: 1.1s;
    animation-iteration-count: infinite;
    opacity: 0;
    border-radius: 50%;
    box-shadow: 0 0 1px 2px rgba(0, 0, 0, $shadowOpacity);
    box-shadow: 0 0 6px 3px rgba($pinColor, 1.0);
}

@keyframes pulsate {
    0% {
        transform: scale(.1, .1);
        opacity: 0;
    }
    50% {
        opacity: 1;
    }
    100% {
        transform: scale(1.2, 1.2);
        opacity: 0;
    }
}

.uk-select {
    border-radius: 25px;
}



.bhm-lang-switcher {
    margin-right: 20px;
    &.uk-light {
        a:not(.uk-badge) {
            color: #fffafa !important;
        }
    }
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class RestaurantMenuItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'number',
        'price',
        'order',
    ];


    public $timestamps = false;

    public function menu(): BelongsTo
    {
        return $this->belongsTo(RestaurantMenu::class);
    }

}

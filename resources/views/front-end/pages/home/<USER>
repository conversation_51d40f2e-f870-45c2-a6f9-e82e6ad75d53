<div class="uk-section uk-section-large uk-section-muted uk-position-relative"
     style="background-color: transparent"
>
    {{--    <div class="uk-position-cover uk-background-cover" uk-img data-src="{{ asset('images/home/<USER>') }}"--}}
    {{--         style="background-size: 250vw"--}}
    {{--    ></div>--}}
    <div class="uk-container uk-container-large uk-position-relative">
        <div class="uk-text-center@s">
            <h2 class="uk-heading-small uk-margin-small-bottom uk-text-uppercase" style="color: rgba(255,255,255,0.9)">
                {!! $companyInfo[$lang]['title'] !!}
            </h2>
            <p class="uk-width-1-2@s uk-margin-auto uk-margin-large-bottom" style="color: rgba(255,255,255,0.9)">
                {!! $companyInfo[$lang]['content'] !!}
            </p>
        </div>

        <div
            class="uk-child-width-1-1 uk-child-width-1-2@s uk-child-width-1-3@m uk-grid-match uk-grid-large uk-flex-center"
            uk-grid>
            @foreach($sectors as $sector)
                <div class="uk-flex uk-flex-column">
                    <div
                        class="uk-card uk-card-default uk-card-small uk-card-hover uk-flex uk-flex-column uk-height-1-1"
                        style="background: transparent; color: rgba(255,255,255,0.9); border: 1px solid #bd9f5740; border-radius: 10px; overflow: hidden;">
                        <div class="uk-card-media-top uk-inline">
                            <div>
                                <img
                                    src="{{ asset('images/home/<USER>/' . $sector['image']['mobile']) }}"
                                    alt="{{ $sector['title'][$lang] }}">
                            </div>
                            <div class="uk-overlay uk-position-cover"
                                 style="background: linear-gradient(180deg, rgba(0,0,0,0) 45%, rgba(0,0,0,0.75) 100%);">
                                <div class="uk-position-bottom-left" style="padding: 15px 20px;">
                                    <h3 class="uk-card-title uk-margin-remove uk-light"
                                        style="color: rgba(255,255,255,0.9)">{{ $sector['title'][$lang] }}</h3>
                                </div>
                            </div>
                        </div>
                        <div class="uk-card-body uk-flex-1 uk-flex uk-flex-column card-body-blur">
                            <div>
                                <p>{!! $sector['description'][$lang] !!}</p>
                            </div>
                            @if($sector['website'])
                                <div class="uk-flex uk-flex-middle uk-margin-top uk-margin-auto-top">
                                    <div class="uk-inline-block uk-margin-small-right"
                                         style="width: 32px; height: 32px; position: relative;">
                                        <img src="{{ asset('images/squircle.svg') }}" width="32" height="32"
                                             style="position: absolute; top: 0; left: 0;">
                                        <svg width="32" height="32" viewBox="0 0 32 32"
                                             xmlns="http://www.w3.org/2000/svg"
                                             style="position: absolute; top: 0; left: 0;">

                                            <path fill="#bd9f57"
                                                  d="M24.52,16.34c0,.18-.09.34-.24.43l-7.35,4.68c-.09.06-.19.08-.28.08-.17,0-.34-.08-.44-.24-.15-.24-.08-.56.16-.71l4.36-2.78c.44-.28.24-.95-.28-.95H7.87c-.29,0-.52-.23-.52-.52h0c0-.29.23-.52.52-.52h12.59c.52,0,.71-.67.28-.95l-4.36-2.77c-.24-.16-.31-.48-.16-.72.16-.24.48-.31.72-.16l7.35,4.68c.15.1.24.25.24.43Z"/>


                                        </svg>
                                    </div>
                                    <a href="{{ $sector['website'] }}"
                                       {{--                                                   class="uk-link-reset"--}}
                                       target="_blank"
                                       style="color: rgba(255,255,255,0.5);">
                                        {{ isset($sector['website-name']) ? $sector['website-name'] : $sector['website'] }}
                                    </a>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            @endforeach
        </div>
    </div>
</div>

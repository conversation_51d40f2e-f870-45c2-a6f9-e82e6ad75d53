<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateRestaurantMenuRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'items' => ['nullable', 'array'],
            'items.*.name' => 'required|string|max:200',

        ];
    }


    public function messages()
    {
        // https://laravel-news.com/catching-laravel-validation-errors-in-vue
        return [

            'items.*.*.max' => 'Maximální délka textu je 200 znaků',
            'items.*.name.required' => 'Název položky musí být vyplněn.',
        ];
    }
}

<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateRestaurantDailyMenuRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }


    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'dates' => ['nullable', 'array'],
            'dates.*.*.*.name' => 'required|string|max:200',

        ];
    }


    public function messages()
    {
        // https://laravel-news.com/catching-laravel-validation-errors-in-vue
        return [

            'dates.*.*.*.max' => 'Maximální délka textu je 200 znaků',
            'dates.*.*.*.name.required' => 'Nelze přidat prázdnou položku. Název musí být vyplněn.',
        ];
    }
}

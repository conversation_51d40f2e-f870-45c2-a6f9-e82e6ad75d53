<script setup>
import AppLayout from '@/Layouts/AppLayout.vue';
import {Link, router} from '@inertiajs/vue3'
import {ref, watch} from 'vue';
import debounce from 'lodash/debounce';
import StripHtml from "@/Components/StripHtml";

const textTitle = 'Nápojový lístek';
const textAddButton = 'Př<PERSON>t kategoirii';

const routeIndex = 'loop.restaurant-drinks.index';
const routeAdd = 'loop.restaurant-drinks.create';
const routeEdit = 'loop.restaurant-drinks.edit';

let props = defineProps({
    result: Object,
    filters: Object
});

let search = ref(props.filters.search);

watch(search, debounce(function (value) {
    router.get(route(routeIndex), {search: value}, {
        preserveState: true,
        replace: true
    });
}, 200));
</script>
<template>
    <app-layout :title="textTitle">
        <template #header>
            <div
                class="uk-child-width-expand@s uk-margin-small-top uk-flex-middle"
                uk-grid
            >

            </div>
        </template>
        <div class="uk-overflow-auto">
            <a class="uk-button uk-button-default" :href="route('restaurant-drinks.pdf')">Stáhnout menu PDF</a>
            <table
                v-if="result.length"
                class="uk-table uk-table-hover uk-table-striped uk-table-middle uk-table-divider"
            >
                <thead>
                <tr>
                    <!--                        <th class="uk-table-shrink" />-->
                    <th class="uk-table-expand">
                        Kategorie
                    </th>
                </tr>
                </thead>
                <tbody>
                <tr
                    v-for="row in result"
                    :key="result.id"
                >
                    <!--                        <td>-->
                    <!--                            <input-->
                    <!--                                class="uk-checkbox"-->
                    <!--                                type="checkbox"-->
                    <!--                            >-->
                    <!--                        </td>-->
                    <td class="uk-table-link">
                        <Link
                            :href="route(routeEdit, row)"
                            class="uk-link-reset"
                        >
                            {{ row.name }}<br/>
                        </Link>
                    </td>
                </tr>
                </tbody>
            </table>
            <div
                v-else
                class="uk-padding-large uk-text-center"
            >
                <h2>Nejsou žádné záznamy</h2>
                <p>Zkuste resetovat filtry (vyhledávání...)</p>
            </div>
        </div>
    </app-layout>
</template>

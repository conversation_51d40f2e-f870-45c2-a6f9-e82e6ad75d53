<?php

namespace App\Http\Controllers\Loop;

use App\Http\Controllers\Controller;
use App\Http\Requests\StoreEventRequest;
use App\Http\Requests\UpdateEventRequest;
use App\Models\Event;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Request;
use Inertia\Inertia;
use Inertia\Response;

class EventController extends Controller
{

    const MODULE_NAME = 'Event';
    const MODULE_ROUTE = 'event';

    public function index(): Response
    {
        return Inertia::render(self::MODULE_NAME.'/Index', [
            'filters' => Request::only('search'),
            'result' => Event::query()
                ->filter(Request::only('search'))
                ->orderBy('id', 'desc')
                ->get(),
        ]);
    }

    public function create(): Response
    {
        return Inertia::render(self::MODULE_NAME.'/Create');
    }


    public function store(StoreEventRequest $request): RedirectResponse
    {
        $event = Event::create($request->validated());

        $event->storeImages($request->safe()->images ?? []);
        $event->storeFiles($request->safe()->media_files ?? []);

        /*
         * EVENT DATES
         * */
        $this->storeDates($event, $request);

        return Redirect::route('loop.'.self::MODULE_ROUTE.'.edit', $event);
    }


    public function edit(Event $event)
    {
        $event['editor'] = $event->editor;
        $event['images'] = $event->images()->orderBy('order')->get();
        $event['media_files'] = $event->files()->orderBy('order')->get();
        $event['dates'] = $event->dates()->orderBy('event_dates.id')->get();

        return Inertia::render(self::MODULE_NAME.'/Edit', [
            'result' => $event,
        ]);
    }

    public function update(Event $event, UpdateEventRequest $request)
    {

        $event->update($request->validated());
        $event->storeImages($request->safe()->images ?? []);
        $event->storeFiles($request->safe()->media_files ?? []);

        /*
         * EVENT DATES
         * */
        $this->storeDates($event, $request);

        return Redirect::back();
    }


    public function destroy(Event $event)
    {
        $event->deleteImages($event->images()->get()); // TODO: when implement restore, move image deletion to pernament delete method

        $event->dates()->delete();

        $event->delete();

        return Redirect::route('loop.'.self::MODULE_ROUTE.'.index');
    }


    public function restore(Event $event)
    {
        $event->restore();

        return Redirect::back();
    }



    protected function storeDates($event, $request)
    {

        $dates = $request->dates ?? array();

        // DELETE
        $datesToKeep = array_filter(Arr::pluck($dates, 'id')); // remove null values with array filter
        $datesToDelete = $event->dates()
            ->whereNotIn('id', $datesToKeep);
        $datesToDelete->delete();

        // UPDATE + CREATE
        foreach ($dates as $date) {

            $data = array(
                'date' => $date['date'],
                'start_at' => $date['start_at'],
                'end_at' => $date['end_at']
            );

            if (isset($date['id'])) {
                $event->dates()->where('id', $date['id'])->update($data);
            } else {
                $event->dates()->create($data);
            }

        }

    }

}

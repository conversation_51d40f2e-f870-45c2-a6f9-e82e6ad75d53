<div class="uk-hidden@s">
    <div style="padding: 0px;  ">
        <video src="/video/v4.mp4" loop muted playsinline uk-video="inview"
               class="uk-object-cover" style="width: 100%; aspect-ratio: 1 / 1;"></video>
    </div>


    <div style="padding: 15px; padding-top: 25px; background-color: white">
        {{--        <h1 class="uk-h6 uk-text-uppercase" style="color: #bd9f57;font-weight: bold">Mission</h1>--}}
        <p class=""
           style="letter-spacing: -0.05em; font-weight: 500; color: #656565; font-size: 21px; line-height: 30px">
            {!! $mission[$lang]['content'] !!}
        </p>
    </div>
</div>

<div class="uk-visible@s">


    <div
    >

        <video
            uk-parallax="opacity: 1,0; blur: 100; easing: 0"
            src="/video/v10.mp4" loop muted playsinline uk-video="inview"
            class="uk-object-cover" style="height: 120vh; aspect-ratio: 16 / 9;"
            width="3840" height="972"></video>
        <!-- Added overlay div -->
                <div class="uk-position-cover uk-overlay-primary"
                     uk-parallax="opacity: 1,0; easing: 0"
                     style="background:
                     /*linear-gradient(180deg, rgba(0,0,0,0.15) 0%, rgba(0,0,0,0) 15%),*/
                     /*linear-gradient(180deg, rgba(0,0,0,0) 85%, rgba(0,0,0,0.45) 100%),*/
                     radial-gradient(circle at 20% 40%, rgba(0,0,0,0.25) 0%, rgba(0,0,0,0) 40%);
                     height: 120vh;"></div>
        <div class="uk-position-cover uk-flex uk-flex-middle">
            <div class="uk-section uk-section-small uk-width-1-1">
                <div class="uk-container uk-container-large">
                    <div class="uk-text-left uk-width-2-5@s uk-light">
                        {{--                        <h1 class="uk-h6 uk-text-uppercase" style="color: #fff;font-weight: bold">Mission</h1>--}}
                        <p style="color:#fff; letter-spacing: -0.05em; font-weight: 500; font-size: 21px; line-height: 30px;">
                            {!! $mission[$lang]['content'] !!}
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>

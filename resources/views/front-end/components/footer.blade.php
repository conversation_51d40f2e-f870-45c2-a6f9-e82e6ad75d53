<style>
    footer {
        --tm-reveal: -20;
    }

    .tm-page:has(> * > .uk-position-z-index-negative) {
        isolation: isolate
    }

    .tm-page > :is(main,footer,.builder):has(> .uk-position-z-index-negative) {
        display: contents;
    }

</style>


<footer>

    <div
        class="uk-section-default  uk-section uk-position-z-index-negative uk-section-large uk-background-norepeat uk-background-cover uk-background-center-center "
        style="z-index: calc(var(--tm-reveal, 0) - 1);
        @if(!isset($showFactsAndFigures))
        padding-top: 20vh
        @endif
        "
        uk-sticky="position: bottom; overflow-flip: true; start: -100%; end: 0;"
        data-src="/images/footer/bg.jpg"
        uk-img>


        <div class="uk-position-cover" style="background-color: rgba(255, 255, 255, 0.96);"></div>

        @if(isset($showFactsAndFigures) && $showFactsAndFigures)
            <div class="uk-container uk-container-large uk-margin-xlarge-bottom">

                <div class="uk-panel uk-margin-xlarge-top">


                    <div style="text-align: center;">
                        <h2 class="uk-heading-small uk-margin-small-bottom uk-text-uppercase" style="color: #656565">
                            {{ __('footer.facts_and_figures') }}
                        </h2>
                    </div>
                </div>

                @php
                    $stats = [
                        [
                            'title_key' => 'assets',
                            'value' => '€ 1100 mil.',
                        ],
                        [
                            'title_key' => 'employees',
                            'value' => '2000',
                        ],
                        [
                            'title_key' => 'active_investments',
                            'value' => '30+',
                        ],
                        [
                            'title_key' => 'countries_of_operation',
                            'value' => '20+',
                        ]
                    ];
                @endphp

                <div
                    class="uk-grid uk-grid-large uk-grid-gap-large uk-margin-large-bottom uk-child-width-1-2 uk-child-width-1-4@m"
                    uk-grid>
                    @foreach($stats as $stat)
                        <div>
                            <div class="uk-panel" style="
                        /*aspect-ratio: 1;*/
                        background-repeat: no-repeat;
                        background-position: center;
                        /*background-size: 100%;*/
                        display: flex;
                        flex-direction: column;
                        justify-content: flex-start;
                        align-items: center;
                        text-align: center;
                        padding-top: 80px;
                    ">
                                <div
                                    class="uk-text-primary uk-h6 uk-margin-remove-bottom uk-margin-remove-bottom uk-text-bolder uk-text-uppercase"
                                    style="color: #656565">
                                    {{ __('footer.' . $stat['title_key']) }}
                                </div>
                                <div class="uk-heading-small uk-margin-top uk-margin-remove-top stat-value"
                                     data-value="{{ $stat['value'] }}">
                                    {{ $stat['value'] }}
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>

            </div>
        @endif


        <div class="uk-container uk-container-large uk-position-relative">


            <h1 class="uk-h6 uk-text-uppercase uk-margin-medium-bottom" style="color: #656565;font-weight: bold">
                {{ __('footer.more_information') }}</h1>

            @php
                $navigation = [
                    [
                        [
                            'route' => 'about',
                            'title_key' => 'about_us'
                        ],
                        [
                            'route' => 'people',
                            'title_key' => 'people'
                        ],
                        [
                            'route' => 'portfolio',
                            'title_key' => 'portfolio'
                        ]
                    ],
                    [
                        [
                            'route' => 'news',
                            'title_key' => 'pressroom'
                        ],
                        [
                            'route' => 'career',
                            'title_key' => 'career'
                        ],
                        [
                            'route' => 'contacts',
                            'title_key' => 'contacts'
                        ]
                    ]
                ];
            @endphp

            <div class="uk-child-width-1-2 uk-child-width-1-3@s uk-grid-large" uk-grid>
                @foreach($navigation as $column)
                    <div>
                        <ul class="uk-list uk-list-divider uk-list-large uk-margin-remove">
                            @foreach($column as $item)
                                <li class="el-item">
                                    <div class="el-content uk-panel">
                                        <div class="uk-inline-block uk-margin-small-right"
                                             style="width: 24px; height: 24px; position: relative;">
                                            <img src="{{ asset('images/squircle-outline.svg') }}" alt="" width="32"
                                                 height="32" style="position: absolute; top: 0; left: 0;">
                                            <svg width="32" height="32" viewBox="0 0 32 32"
                                                 xmlns="http://www.w3.org/2000/svg"
                                                 style="position: absolute; top: 0; left: 0;">
                                                <path fill="#bd9f57"
                                                      d="M24.52,16.34c0,.18-.09.34-.24.43l-7.35,4.68c-.09.06-.19.08-.28.08-.17,0-.34-.08-.44-.24-.15-.24-.08-.56.16-.71l4.36-2.78c.44-.28.24-.95-.28-.95H7.87c-.29,0-.52-.23-.52-.52h0c0-.29.23-.52.52-.52h12.59c.52,0,.71-.67.28-.95l-4.36-2.77c-.24-.16-.31-.48-.16-.72.16-.24.48-.31.72-.16l7.35,4.68c.15.1.24.25.24.43Z"/>
                                            </svg>
                                        </div>
                                        <a href="{{ route(locale().'.'.$item['route']) }}"
                                           class="uk-h5 uk-link-text uk-margin-remove-last-child">
                                            {{ __('footer.' . $item['title_key']) }}
                                        </a>
                                    </div>
                                </li>
                            @endforeach
                        </ul>
                    </div>
                @endforeach
                <div class="uk-flex uk-flex-right@s uk-width-1-1 uk-width-1-3@s">


                    <div class="uk-grid uk-flex uk-flex-middle uk-visible@s" uk-grid>
                        {{-- LinkedIn Logo --}}
                        <div class="uk-width-auto">
                            <div
                                style="width: 64px; height: 64px; position: relative; line-height: 64px; text-align: center;">
                                <img alt="" src="{{ asset('images/squircle.svg') }}" width="64" height="64"
                                     style="position: absolute; top: 0; left: 0;">
                                <span uk-icon="icon: linkedin;ratio: 1.5"></span>
                            </div>
                        </div>
                        {{-- LinkedIn Text --}}
                        <div class="uk-width-expand" style="padding-left: 20px">
                            <h2 class="uk-h3 uk-margin-remove" style="text-align: left; line-height: 1.2">
                                <a style="color: #656565; " href="https://www.linkedin.com/company/bohemia-industry"
                                   target="_blank">
                                    {{ __('footer.follow_us') }} <br
                                        class="uk-visible@s"/>{{ __('footer.on_linkedin') }}
                                </a>
                            </h2>
                        </div>
                    </div>
                    <div class="uk-grid uk-margin-remove-top uk-flex uk-flex-middle uk-hidden@s" uk-grid>
                        {{-- LinkedIn Logo --}}
                        <div class="uk-width-auto">
                            <div
                                style="width: 24px; height: 24px; position: relative; line-height: 24px; text-align: center;">
                                <img alt="" src="{{ asset('images/squircle.svg') }}" width="24" height="24"
                                     style="position: absolute; top: 0; left: 0;">
                                <span uk-icon="icon: linkedin;ratio: 0.5"></span>
                            </div>
                        </div>
                        {{-- LinkedIn Text --}}
                        <div class="uk-width-expand" style="padding-left: 15px">
                            <h2 class="uk-h4 uk-margin-remove" style="text-align: left; line-height: 1.2">
                                <a style="color: #656565; " href="https://www.linkedin.com/company/bohemia-industry"
                                   target="_blank">
                                    {{ __('footer.follow_us') }} <br
                                        class="uk-visible@s"/>{{ __('footer.on_linkedin') }}
                                </a>
                            </h2>
                        </div>
                    </div>


                </div>
            </div>

            <hr class="uk-margin-large"/>

            <div class="uk-grid" uk-grid>
                <div class="uk-width-1-3@s">
                    <a href="{{route(locale().'.home')}}">
                        <img class="uk-margin-small-bottom" src="{{ asset('/images/logo-obsidian.svg') }}" width="120"
                             alt="BHM Group" height=""></a>
                    <br/>
                    <span class="uk-text-">{{ __('footer.creating_value_together') }}</span>

                </div>
                <div class="uk-width-1-3">


                </div>
                <div class="uk-width-1-3@s uk-text-right@s">
                    © 2025 BHM group a.s. {{ __('footer.all_rights_reserved') }}
                </div>
            </div>
        </div>


    </div>
</footer>



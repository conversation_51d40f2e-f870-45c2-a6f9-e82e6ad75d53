// 1. Your custom variables and variable overwrites.
//$global-link-color: #DA7D02;

// 2. Import default variables and available mixins.
@import "uikit/src/scss/variables-theme.scss";
@import "uikit/src/scss/mixins-theme.scss";

// 3. Your custom mixin overwrites.
//@mixin hook-card() { color: #000; }

// 4. Import UIkit.
@import "uikit/src/scss/uikit-theme.scss";

@import url('https://fonts.googleapis.com/css?family=IBM+Plex+Sans:300,300i,400,400i,500,500i,600,600i,700,700i&display=swap&subset=latin-ext');


$xo-nav-width: 250px;

.xo-layout-top {
    position: fixed;
    height: 80px;
    line-height: 80px;
    background: red;
    width: 100%;
    margin-bottom: 80px;
}

.xo-layout-left {
    // width: $xo-nav-width;
    position: fixed;
}

.xo-layout-right {
    margin-left: $width-medium-width;
}



.uk-pagination {
    .sr-only {
        display: none;
    }
    .active {
        text-decoration: underline;
        font-weight: bold;
    }
}



em {
    color: #c97f4a;
}

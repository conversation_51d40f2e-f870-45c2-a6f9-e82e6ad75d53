<script setup>
import { Head, Link, useForm } from '@inertiajs/vue3';
import AuthenticationCard from '@/Components/AuthenticationCard.vue';
import AuthenticationCardLogo from '@/Components/AuthenticationCardLogo.vue';
import Checkbox from '@/Components/Checkbox.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import TextInput from '@/Components/TextInput.vue';

defineProps({
    canResetPassword: Boolean,
    status: String,
});

const form = useForm({
    email: '',
    password: '',
    remember: false,
});

const submit = () => {
    form.transform(data => ({
        ...data,
        remember: form.remember ? 'on' : '',
    })).post(route('login'), {
        onFinish: () => form.reset('password'),
    });
};
</script>

<template>
    <Head title="Log in" />

    <AuthenticationCard>
        <template #logo>
            <AuthenticationCardLogo />
        </template>

        <div v-if="status" class="mb-4 font-medium text-sm text-green-600">
            {{ status }}
        </div>

        <form @submit.prevent="submit">

            <div class="uk-overflow-auto uk-width-large uk-margin-auto">
                <table class="uk-table uk-table-middle">

                    <tbody>
                    <tr>
                        <td class="uk-table-shrink" style="width: 60px;">

                            <span style="color:white" uk-icon="icon: user; ratio: 2"></span>

                        </td>

                        <td>

                            <div class="uk-form-controls">
                                <InputLabel for="email" value="Email" />
                                <TextInput
                                    id="email"
                                    v-model="form.email"
                                    type="email"
                                    class="mt-1 block w-full"
                                    required
                                    autofocus
                                    autocomplete="username"
                                />
                                <InputError class="mt-2" :message="form.errors.email" />
                            </div>
                        </td>
                    </tr>
                    <tr class="">
                        <td>
                            <span style="color:white" uk-icon="icon: shield-lock; ratio: 2"></span>
                        </td>

                        <td>
                            <InputLabel for="password" value="Heslo" />
                            <TextInput
                                id="password"
                                v-model="form.password"
                                type="password"
                                class="mt-1 block w-full"
                                required
                                autocomplete="current-password"
                            />
                            <InputError class="mt-2" :message="form.errors.password" />
                        </td>
                    </tr>
                    <tr>
                        <td></td>
                        <td>

                            <div class="uk-grid">
                                <div>
                                    <label class="flex items-center">
                                        <Checkbox v-model:checked="form.remember" name="remember" />
                                        <span class="ml-2 text-sm text-gray-600"> Zůstat přihlášen</span>
                                    </label>
                                </div>
                                <div>
<!--                                    <Link v-if="canResetPassword" :href="route('password.request')" class="underline text-sm text-gray-600 hover:text-gray-900 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">-->
<!--                                        Forgot your password?-->
<!--                                    </Link>-->

                                </div>
                            </div>

                        </td>
                    </tr>
                    <tr>
                        <td></td>
                        <td>


                            <PrimaryButton class="ml-4" :class="{ 'opacity-25': form.processing }" :disabled="form.processing">
                                Přihlásit
                            </PrimaryButton>
                        </td>
                    </tr>
                    </tbody>

                </table>

            </div>


        </form>

    </AuthenticationCard>
</template>

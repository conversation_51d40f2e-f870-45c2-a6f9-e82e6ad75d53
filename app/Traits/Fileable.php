<?php

namespace App\Traits;

use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Storage;
use Intervention\Image\Facades\Image;
use <PERSON>tie\LaravelImageOptimizer\Facades\ImageOptimizer;

trait Fileable
{
    // TODO: implement private and public files mode
    public function storeFiles($files, $formats = [])
    {
        // DELETE removed
        $itemsToKeep = array_filter(Arr::pluck($files, 'id')); // remove null values with array filter
        $filesToDelete = $this->files()->whereNotIn('id', $itemsToKeep);
        $this->deleteFiles($filesToDelete->get());
        $filesToDelete->delete();

        // CREATE OR UPDATE
        foreach ($files as $key => $file) {

            if (isset($file['fileData'])) { // new file
                $this->files()->create([
                    'file' => $file['fileData']->store('files', 'public'),
                    'name' => $file['name'],
                    'order' => $key,
                    'size' => $file['fileData']->getSize(),
                    'type' => $file['fileData']->getClientMimeType(),
                ]);
            } else {
                $this->files()->where('id', $file['id'])->update([
                    'name' => $file['name'],
                    'order' => $key,
                ]);
            }
        }
    }


    public function deleteFiles($files)
    {
        foreach ($files as $file) {
            Storage::disk('public')->delete($file->file);
        }
    }

    private function handleDeletedFiles($files)
    {

    }
}

@extends('front-end.layouts.app')

@section('title', __('navigation.about_us'))


@section('content')

    <div class="uk-margin-remove-top uk-section uk-padding-remove about-page"
         uk-scrollspy="target: [uk-scrollspy-class]; cls: uk-animation-fade; delay: false;">

        @include('front-end.components.navbar', ['navMode' => 'white'])

        <!-- Mobile sections container with flexbox ordering -->
        <div class="uk-flex uk-flex-column">
            <!-- About Us section -->
            <div class="mobile-section-order-2">
                <div class="uk-flex-middle uk-flex" style="min-height: 50vh">
                    <div
                        class="uk-container uk-margin-large-top uk-margin-large-bottom uk-container-large uk-padding-remove-bottom">
                        <div class="uk-grid">
                            <div class="uk-width-3-5@m uk-margin-auto uk-text-center@s">
                                <h2 class="uk-heading-small uk-margin-small-bottom uk-text-uppercase">
                                    {!! $aboutContent[$lang]['title'] !!}
                                </h2>

                                <p class="uk-margin" uk-scrollspy-class>
                                    {!! $aboutContent[$lang]['content'] !!}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Creating value together section -->
            <div class=" mobile-section-order-1">
                <div
                    data-src="/images/about/bg-2.jpg"
                    loading="eager" uk-img
                    class="uk-background-norepeat uk-background-cover
                    uk-background-top-center uk-background-fixed uk-section uk-flex uk-flex-middle"
                    style="min-height: 50vh">

                    <div class="uk-width-1-1">
                        <div class="uk-container uk-container-large">
                            <h1 class="uk-heading-large uk-text-center about-claim"
                                uk-scrollspy-class>
                                Creating
                                value
                                together
                            </h1>
                        </div>
                    </div>
                </div>
            </div>

            <!-- History and Social Responsibility section -->
            <div class="uk-section uk-section-large mobile-section-order-3 about-history-section">
                <div class="uk-container uk-container-large">
                    <div class="uk-width-3-5@s uk-margin-auto uk-text-center@s">
                        <h2 class="uk-heading-small uk-margin-bottom uk-text-uppercase">
                            {!! $historyContent[$lang]['title'] !!}
                        </h2>
                        <div>
                            {!! $historyContent[$lang]['content'] !!}
                        </div>
                    </div>

                </div>
            </div>

            <div class="uk-section mobile-section-order-4 about-social-responsibility">
                <div class="uk-container uk-container-large uk-margin-xlarge-bottom">

                    <div class="uk-width-3-5@s uk-margin-auto uk-text-center@s">

                        <h2 class="uk-heading-small uk-margin uk-text-uppercase">
                            {!! $socialResponsibilityContent[$lang]['title'] !!}
                        </h2>
                        {!! $socialResponsibilityContent[$lang]['content'] !!}

                        <div
                            class="uk-child-width-1-2 uk-child-width-1-4@m uk-margin-large-top uk-grid uk-text-center"
                            uk-grid>
                            @foreach ($foundationPartners as $partner)
                                <div class="uk-padding-small">
                                    <a class="about-foundation-link" href="{{$partner['link']}}"> <img
                                            src="/images/foundation/{{$partner['img']}}.svg"
                                            style="height: 100%; max-height: 60px; max-width: 120px; border-radius: 0"
                                            alt=""> </a>
                                </div>
                            @endforeach
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>

    @include('front-end.components.footer')

@endsection

{"private": true, "scripts": {"development": "mix", "watch": "mix watch", "watch-poll": "mix watch -- --watch-options-poll=1000", "hot": "mix watch --hot", "production": "mix --production"}, "devDependencies": {"@inertiajs/vue3": "^1.0.0", "autoprefixer": "^10.4.7", "axios": "^1.1.2", "laravel-mix": "^6.0.49", "resolve-url-loader": "^5.0.0", "sass": "^1.66.1", "sass-loader": "^12.6.0", "uikit": "^3.19.1", "vue": "^3.4.21", "vue-loader": "^16.8.3", "vuedraggable": "^4.1.0"}, "dependencies": {"@tiptap/pm": "^2.2.4", "@tiptap/starter-kit": "^2.2.4", "@tiptap/vue-3": "^2.2.4", "animejs": "^3.2.2", "vue-datepicker-next": "^1.0.3"}}
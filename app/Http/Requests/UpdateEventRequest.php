<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateEventRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'title' => ['required', 'max:100'],
            'content' => ['required'],
            'price' => ['nullable', 'string'],
            'place' => ['nullable', 'string'],
            'ticket_url' => ['nullable', 'url'],
            'images' => ['nullable', 'array'],
            'media_files' => ['nullable', 'array'],
        ];
    }
}

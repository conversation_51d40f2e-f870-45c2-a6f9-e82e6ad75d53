<?php

namespace App\Models;

use App\Traits\Fileable;
use App\Traits\Imageable;
use Cviebrock\EloquentSluggable\Sluggable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Event extends Model
{
    use Sluggable, HasFactory, Imageable, Fileable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'title',
        'content',
        'price',
        'place',
        'ticket_url',
        'created_by',
        'edited_by',
    ];


    public static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            $model->created_by = $model->created_by ?? auth()->user()->id;
            $model->edited_by = $model->edited_by ?? auth()->user()->id;
        });

        static::updating(function ($model) {
            $model->edited_by = $model->edited_by ?? auth()->user()->id;
            $model->updated_at = now();
        });
    }


    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }


    public function editor()
    {
        return $this->belongsTo(User::class, 'edited_by');
    }


    public function images()
    {
        return $this->morphMany(Image::class, 'imageable');
    }


    public function files()
    {
        return $this->morphMany(File::class, 'fileable');
    }


    public function dates()
    {
        return $this->hasMany(EventDate::class);
    }


    public function scopeFilter($query, array $filters)
    {
        $query->when($filters['search'] ?? null, function ($query, $search) {
            $query->where(function ($query) use ($search) {
                $query->where('title', 'like', '%' . $search . '%')
                    ->orWhere('content', 'like', '%' . $search . '%');
            });
        });
    }


    public function sluggable(): array
    {
        return [
            'slug' => [
                'source' => 'title'
            ]
        ];
    }
}

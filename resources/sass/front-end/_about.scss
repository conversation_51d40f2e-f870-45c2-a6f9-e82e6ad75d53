.about-page {

    background-image: url('/images/home/<USER>');
    background-size: 250%;

    .uk-heading-medium, .uk-heading-small {
        color: #fffafa;
    }

    p {
        color: rgba(255, 250, 250, 0.9);
    }

    .about-social-responsibility {

        padding-bottom: 20px;

        @media (max-width: 958px) {
            padding-top: 80px;
        }

        @media (min-width: 959px) {
            padding-top: 0;

        }
    }


    @media (max-width: 959px) {

        background-size: 3000px;
        background-position: center center;

        .mobile-section-order-1 {
            order: 1;
        }

        .mobile-section-order-2 {
            order: 2;
        }

        .mobile-section-order-3 {
            order: 3;
        }
        .mobile-section-order-4 {
            order: 4;
        }

        /* Break up monotony of black background in history section */
        .about-history-section {
            background-color: white;
            background-image: url('/images/home/<USER>');
            background-size: 1000px;
            background-position: center center;
            color: black;


            .uk-heading-medium, .uk-heading-small {
                color: #656565;
            }

            p {
                color: #0b1215;
            }
        }

    }
}

.about-claim {
    color: rgba(255, 250, 250, 0.4);


    @media (max-width: 767px) {
        color: rgba(255, 255, 255, 0.9);
    }
}

.about-foundation-link {
    img {
        opacity: 0.9;
    }
    &:hover {
        img {
            opacity: 1;
        }
    }
}

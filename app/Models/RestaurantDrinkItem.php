<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class RestaurantDrinkItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'number',
        'price',
        'order',
        'volume',
    ];

    public $timestamps = false;

    public function menu(): BelongsTo
    {
        return $this->belongsTo(RestaurantDrink::class);
    }


}

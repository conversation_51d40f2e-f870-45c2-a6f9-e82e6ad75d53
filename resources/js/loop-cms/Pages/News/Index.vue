<script setup>
import AppLayout from '@/Layouts/AppLayout.vue';
import {Link, router} from '@inertiajs/vue3'
import {ref, watch} from 'vue';
import debounce from 'lodash/debounce';
import StripHtml from "@/Components/StripHtml";

const textTitle = 'Novinky';
const textAddButton = 'Př<PERSON>t novinku';

const routeIndex = 'loop.news.index';
const routeAdd = 'loop.news.create';
const routeEdit = 'loop.news.edit';

let props = defineProps({
    result: Object,
    filters: Object
});

let search = ref(props.filters.search);

watch(search, debounce(function (value) {
    router.get(route(routeIndex), {search: value}, {
        preserveState: true,
        replace: true
    });
}, 200));
</script>
<template>
    <app-layout :title="textTitle">
        <template #header>
            <div
                class="uk-child-width-expand@s uk-margin-small-top uk-flex-middle"
                uk-grid
            >
                <div class="uk-width-expand">
                    <div
                        class="uk-child-width-expand@s"
                        uk-grid
                    >
                        <div class="uk-width-expand">
                            <form class="uk-search uk-search-default uk-width-5-6">
                                <span uk-search-icon/> <input
                                id="search"
                                v-model="search"
                                class="uk-search-input uk-flex-wrap-stretch"
                                type="search"
                                autocomplete="off"
                                placeholder="Search..."
                            >
                            </form>
                        </div>
                    </div>
                </div>
                <div class="uk-width-auto">
                    <Link
                        class="uk-button uk-button-primary uk-icon uk-align-right"
                        :href="route(routeAdd)"
                    >
                        <span
                            uk-icon="icon: plus; "
                        /> {{textAddButton}}
                    </Link>
                </div>
            </div>
        </template>
        <div class="uk-overflow-auto">
            <table
                v-if="result.length"
                class="uk-table uk-table-hover uk-table-striped uk-table-middle uk-table-divider"
            >
                <thead>
                <tr>
                    <!--                        <th class="uk-table-shrink" />-->
                    <th class="uk-table-expand">
                        Titulek
                    </th>
                    <th class="uk-width-medium">
                        Text
                    </th>
                </tr>
                </thead>
                <tbody>
                <tr
                    v-for="row in result"
                    :key="result.id"
                >
                    <!--                        <td>-->
                    <!--                            <input-->
                    <!--                                class="uk-checkbox"-->
                    <!--                                type="checkbox"-->
                    <!--                            >-->
                    <!--                        </td>-->
                    <td class="uk-table-link">
                        <Link
                            :href="route(routeEdit, row)"
                            class="uk-link-reset"
                        >
                            {{ row.title }}<br/>
                            <strip-html :message="row.content" />
                        </Link>
                    </td>
                    <td class="uk-text-truncate">
                    </td>
                </tr>
                </tbody>
            </table>
            <div
                v-else
                class="uk-padding-large uk-text-center"
            >
                <h2>Nejsou žádné záznamy</h2>
                <p>Zkuste resetovat filtry (vyhledávání...)</p>
            </div>
        </div>
    </app-layout>
</template>

<?php

namespace App\Traits;

use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Storage;
use Intervention\Image\Drivers\Gd\Driver;
use Intervention\Image\Facades\Image;
use Intervention\Image\ImageManager;

trait Imageable
{
    // TODO: implement image processing for different formats (etc. classic, square, thumbnail + define sizes)
    public function storeImages($images, $formats = [])
    {
        // DELETE removed
        $itemsToKeep = array_filter(Arr::pluck($images, 'id')); // remove null values with array filter
        $imagesToDelete = $this->images()->whereNotIn('id', $itemsToKeep);
        $this->deleteImages($imagesToDelete->get());
        $imagesToDelete->delete();

        // CREATE OR UPDATE
        foreach ($images as $key => $image) {
            if (isset($image['fileData'])) { // new file

                // store image
                $imageFileName = $image['fileData']->store('images', 'public');
                // optimize image
                $imagePath = public_path('storage/' . $imageFileName);
                $manager = new ImageManager(new Driver());
                $manager->read($imagePath)->scaleDown(1600, 1600)->save($imagePath, 30);

                // store data
                $this->images()->create([
                    'file' => $imageFileName,
                    'name' => $image['name'],
                    'order' => $key,
                    'size' => $image['fileData']->getSize(),
                    'type' => $image['fileData']->getClientMimeType(),
                ]);


            } else {
                $this->images()->where('id', $image['id'])->update([
                    'name' => $image['name'],
                    'order' => $key,
                ]);
            }
        }
    }


    public function deleteImages($files)
    {
        foreach ($files as $file) {
            Storage::disk('public')->delete($file->file);
        }
    }
}

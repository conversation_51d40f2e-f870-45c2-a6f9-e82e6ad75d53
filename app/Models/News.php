<?php

namespace App\Models;

use App\Traits\Fileable;
use App\Traits\Imageable;
use C<PERSON>brock\EloquentSluggable\Sluggable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class News extends Model
{
    use Sluggable, HasFactory, Imageable, Fileable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'title',
        'content',
        'important',
        'created_by',
        'edited_by',
    ];


    public static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            $model->created_by = $model->created_by ?? auth()->user()->id;
            $model->edited_by = $model->edited_by ?? auth()->user()->id;
        });

        static::updating(function ($model) {
            $model->edited_by = $model->edited_by ?? auth()->user()->id;
            $model->updated_at = now();
        });
    }


    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }


    public function editor()
    {
        return $this->belongsTo(User::class, 'edited_by');
    }


    public function images()
    {
        return $this->morphMany(Image::class, 'imageable');
    }


    public function files()
    {
        return $this->morphMany(File::class, 'fileable');
    }


    public function scopeFilter($query, array $filters)
    {
        $query->when($filters['search'] ?? null, function ($query, $search) {
            $query->where(function ($query) use ($search) {
                $query->where('title', 'like', '%' . $search . '%')
                    ->orWhere('content', 'like', '%' . $search . '%');
            });
        });

        /*$query->when($filters['search'] ?? null, function ($query, $search) {
            $query->where(function ($query) use ($search) {
                $query->where('first_name', 'like', '%'.$search.'%')
                    ->orWhere('last_name', 'like', '%'.$search.'%')
                    ->orWhere('email', 'like', '%'.$search.'%');
            });
        })->when($filters['role'] ?? null, function ($query, $role) {
            $query->whereRole($role);
        })->when($filters['trashed'] ?? null, function ($query, $trashed) {
            if ($trashed === 'with') {
                $query->withTrashed();
            } elseif ($trashed === 'only') {
                $query->onlyTrashed();
            }
        });*/
    }


    public function sluggable(): array
    {
        return [
            'slug' => [
                'source' => 'title'
            ]
        ];
    }

}

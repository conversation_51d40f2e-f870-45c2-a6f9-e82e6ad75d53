{"codeToName": {"13": "CR", "32": "space", "33": "exclam", "34": "quotedbl", "35": "numbersign", "36": "dollar", "37": "percent", "38": "ampersand", "39": "<PERSON><PERSON><PERSON>", "40": "parenleft", "41": "parenright", "42": "asterisk", "43": "plus", "44": "comma", "45": "hyphen", "46": "period", "47": "slash", "48": "zero", "49": "one", "50": "two", "51": "three", "52": "four", "53": "five", "54": "six", "55": "seven", "56": "eight", "57": "nine", "58": "colon", "59": "semicolon", "60": "less", "61": "equal", "62": "greater", "63": "question", "64": "at", "65": "A", "66": "B", "67": "C", "68": "D", "69": "E", "70": "F", "71": "G", "72": "H", "73": "I", "74": "J", "75": "K", "76": "L", "77": "M", "78": "N", "79": "O", "80": "P", "81": "Q", "82": "R", "83": "S", "84": "T", "85": "U", "86": "V", "87": "W", "88": "X", "89": "Y", "90": "Z", "91": "bracketleft", "92": "backslash", "93": "bracketright", "94": "asciicircum", "95": "underscore", "96": "grave", "97": "a", "98": "b", "99": "c", "100": "d", "101": "e", "102": "f", "103": "g", "104": "h", "105": "i", "106": "j", "107": "k", "108": "l", "109": "m", "110": "n", "111": "o", "112": "p", "113": "q", "114": "r", "115": "s", "116": "t", "117": "u", "118": "v", "119": "w", "120": "x", "121": "y", "122": "z", "123": "braceleft", "124": "bar", "125": "braceright", "126": "asciitilde", "160": "uni00A0", "161": "exclamdown", "162": "cent", "163": "sterling", "164": "currency", "165": "yen", "166": "brokenbar", "167": "section", "168": "<PERSON><PERSON><PERSON>", "169": "copyright", "170": "ordfeminine", "171": "guillemotleft", "172": "logicalnot", "174": "registered", "175": "macron", "176": "degree", "177": "plus<PERSON>us", "178": "uni00B2", "179": "uni00B3", "180": "acute", "181": "uni00B5", "182": "paragraph", "183": "periodcentered", "184": "cedilla", "185": "uni00B9", "186": "ordmasculine", "187": "guil<PERSON><PERSON><PERSON>", "188": "onequarter", "189": "onehalf", "190": "threequarters", "191": "questiondown", "192": "<PERSON><PERSON>", "193": "Aacute", "194": "Acircumflex", "195": "<PERSON><PERSON>", "196": "Adieresis", "197": "<PERSON><PERSON>", "198": "AE", "199": "Ccedilla", "200": "<PERSON><PERSON>", "201": "Eacute", "202": "Ecircumflex", "203": "Edieresis", "204": "<PERSON><PERSON>", "205": "Iacute", "206": "Icircumflex", "207": "Idieresis", "208": "Eth", "209": "Ntilde", "210": "<PERSON><PERSON>", "211": "Oacute", "212": "Ocircumflex", "213": "<PERSON><PERSON><PERSON>", "214": "Odieresis", "215": "multiply", "216": "<PERSON><PERSON><PERSON>", "217": "<PERSON><PERSON>", "218": "Uacute", "219": "Ucircumflex", "220": "Udieresis", "221": "Ya<PERSON>", "222": "Thorn", "223": "germandbls", "224": "agrave", "225": "aacute", "226": "acircumflex", "227": "atilde", "228": "adieresis", "229": "aring", "230": "ae", "231": "ccedilla", "232": "egrave", "233": "eacute", "234": "ecircumflex", "235": "edieresis", "236": "igrave", "237": "iacute", "238": "icircumflex", "239": "idieresis", "240": "eth", "241": "ntilde", "242": "ograve", "243": "oacute", "244": "ocircumflex", "245": "otilde", "246": "odieresis", "247": "divide", "248": "oslash", "249": "ugrave", "250": "uacute", "251": "ucircumflex", "252": "udieresis", "253": "yacute", "254": "thorn", "255": "ydieresis", "256": "Amacron", "257": "amacron", "258": "Abreve", "259": "abreve", "260": "Aogonek", "261": "aogonek", "262": "Cacute", "263": "cacute", "264": "Ccircumflex", "265": "ccircumflex", "266": "Cdotaccent", "267": "cdotaccent", "268": "<PERSON><PERSON><PERSON>", "269": "ccaron", "270": "<PERSON><PERSON><PERSON>", "271": "dcaron", "272": "Dcroat", "273": "dmacron", "274": "Emacron", "275": "emacron", "276": "Ebreve", "277": "ebreve", "278": "Edotaccent", "279": "edotaccent", "280": "Eogonek", "281": "eogonek", "282": "<PERSON><PERSON><PERSON>", "283": "ecaron", "284": "Gcircumflex", "285": "gcircumflex", "286": "Gbreve", "287": "gbreve", "288": "Gdotaccent", "289": "gdotaccent", "290": "uni0122", "291": "uni0123", "292": "Hcircumflex", "293": "hcircumflex", "294": "H<PERSON>", "295": "hbar", "296": "Itilde", "297": "itilde", "298": "Imacron", "299": "imacron", "300": "Ibreve", "301": "i<PERSON>ve", "302": "Iogonek", "303": "iogonek", "304": "Idot", "305": "dotlessi", "306": "IJ", "307": "ij", "308": "Jcircumflex", "309": "jcircumflex", "310": "uni0136", "311": "uni0137", "312": "kgreenlandic", "313": "<PERSON><PERSON>", "314": "lacute", "315": "uni013B", "316": "uni013C", "317": "<PERSON><PERSON><PERSON>", "318": "lcaron", "319": "Ldot", "320": "ldot", "321": "Lslash", "322": "lslash", "323": "Nacute", "324": "nacute", "325": "uni0145", "326": "uni0146", "327": "<PERSON><PERSON><PERSON>", "328": "ncaron", "329": "napostrophe", "330": "Eng", "331": "eng", "332": "Omacron", "333": "omacron", "334": "Obreve", "335": "obreve", "336": "Ohungarumlau<PERSON>", "337": "ohungarumlaut", "338": "OE", "339": "oe", "340": "<PERSON><PERSON>", "341": "racute", "342": "uni0156", "343": "uni0157", "344": "<PERSON><PERSON><PERSON>", "345": "rcaron", "346": "Sacute", "347": "sacute", "348": "Scircumflex", "349": "scircumflex", "350": "Scedilla", "351": "scedilla", "352": "<PERSON><PERSON><PERSON>", "353": "scaron", "354": "uni0162", "355": "uni0163", "356": "<PERSON><PERSON><PERSON>", "357": "tcaron", "358": "Tbar", "359": "tbar", "360": "Utilde", "361": "utilde", "362": "Umacron", "363": "umacron", "364": "Ubreve", "365": "ubreve", "366": "<PERSON><PERSON>", "367": "uring", "368": "Uhungaru<PERSON>lau<PERSON>", "369": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "370": "Uogonek", "371": "uogonek", "372": "Wcircumflex", "373": "wcircumflex", "374": "Ycircumflex", "375": "ycircumflex", "376": "Ydieresis", "377": "Zacute", "378": "zacute", "379": "Zdotaccent", "380": "zdotaccent", "381": "<PERSON><PERSON><PERSON>", "382": "z<PERSON>on", "383": "longs", "399": "uni018F", "402": "florin", "416": "<PERSON><PERSON>", "417": "ohorn", "431": "<PERSON><PERSON>", "432": "uhorn", "496": "uni01F0", "506": "Aringacute", "507": "aring<PERSON><PERSON>", "508": "AEacute", "509": "aeacute", "510": "Oslashacute", "511": "oslashacute", "536": "uni0218", "537": "uni0219", "538": "uni021A", "539": "uni021B", "567": "uni0237", "601": "uni0259", "700": "uni02BC", "710": "circumflex", "711": "caron", "713": "uni02C9", "728": "breve", "729": "dotaccent", "730": "ring", "731": "ogonek", "732": "tilde", "733": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "755": "uni02F3", "768": "gravecomb", "769": "acutecomb", "770": "uni0302", "771": "tildecomb", "772": "uni0304", "774": "uni0306", "775": "uni0307", "776": "uni0308", "777": "hookab<PERSON><PERSON>", "778": "uni030A", "779": "uni030B", "780": "uni030C", "783": "uni030F", "786": "uni0312", "795": "uni031B", "803": "dotbelowcomb", "805": "uni0325", "806": "uni0326", "807": "uni0327", "808": "uni0328", "821": "uni0335", "822": "uni0336", "823": "uni0337", "824": "uni0338", "900": "tonos", "901": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "902": "Alphatonos", "903": "anoteleia", "904": "Epsilontonos", "905": "Etatonos", "906": "Iotatonos", "908": "Omicrontonos", "910": "Upsilontonos", "911": "Omegatonos", "912": "iotadieresistonos", "913": "Alpha", "914": "Beta", "915": "Gamma", "916": "uni0394", "917": "Epsilon", "918": "Zeta", "919": "Eta", "920": "Theta", "921": "Iota", "922": "Kappa", "923": "Lambda", "924": "Mu", "925": "<PERSON>u", "926": "Xi", "927": "Omicron", "928": "Pi", "929": "Rho", "931": "Sigma", "932": "Tau", "933": "Upsilon", "934": "Phi", "935": "<PERSON>", "936": "Psi", "937": "uni03A9", "938": "Iotadieresis", "939": "Upsilondieresis", "940": "alphatonos", "941": "epsilontonos", "942": "etatonos", "943": "iotatonos", "944": "upsilondieresistonos", "945": "alpha", "946": "beta", "947": "gamma", "948": "delta", "949": "epsilon", "950": "zeta", "951": "eta", "952": "theta", "953": "iota", "954": "kappa", "955": "lambda", "956": "uni03BC", "957": "nu", "958": "xi", "959": "omicron", "960": "pi", "961": "rho", "962": "uni03C2", "963": "sigma", "964": "tau", "965": "upsilon", "966": "phi", "967": "chi", "968": "psi", "969": "omega", "970": "iotadieresis", "971": "upsilondieresis", "972": "omicrontonos", "973": "upsilontonos", "974": "omegatonos", "977": "uni03D1", "978": "uni03D2", "982": "uni03D6", "1024": "uni0400", "1025": "uni0401", "1026": "uni0402", "1027": "uni0403", "1028": "uni0404", "1029": "uni0405", "1030": "uni0406", "1031": "uni0407", "1032": "uni0408", "1033": "uni0409", "1034": "uni040A", "1035": "uni040B", "1036": "uni040C", "1037": "uni040D", "1038": "uni040E", "1039": "uni040F", "1040": "uni0410", "1041": "uni0411", "1042": "uni0412", "1043": "uni0413", "1044": "uni0414", "1045": "uni0415", "1046": "uni0416", "1047": "uni0417", "1048": "uni0418", "1049": "uni0419", "1050": "uni041A", "1051": "uni041B", "1052": "uni041C", "1053": "uni041D", "1054": "uni041E", "1055": "uni041F", "1056": "uni0420", "1057": "uni0421", "1058": "uni0422", "1059": "uni0423", "1060": "uni0424", "1061": "uni0425", "1062": "uni0426", "1063": "uni0427", "1064": "uni0428", "1065": "uni0429", "1066": "uni042A", "1067": "uni042B", "1068": "uni042C", "1069": "uni042D", "1070": "uni042E", "1071": "uni042F", "1072": "uni0430", "1073": "uni0431", "1074": "uni0432", "1075": "uni0433", "1076": "uni0434", "1077": "uni0435", "1078": "uni0436", "1079": "uni0437", "1080": "uni0438", "1081": "uni0439", "1082": "uni043A", "1083": "uni043B", "1084": "uni043C", "1085": "uni043D", "1086": "uni043E", "1087": "uni043F", "1088": "uni0440", "1089": "uni0441", "1090": "uni0442", "1091": "uni0443", "1092": "uni0444", "1093": "uni0445", "1094": "uni0446", "1095": "uni0447", "1096": "uni0448", "1097": "uni0449", "1098": "uni044A", "1099": "uni044B", "1100": "uni044C", "1101": "uni044D", "1102": "uni044E", "1103": "uni044F", "1104": "uni0450", "1105": "uni0451", "1106": "uni0452", "1107": "uni0453", "1108": "uni0454", "1109": "uni0455", "1110": "uni0456", "1111": "uni0457", "1112": "uni0458", "1113": "uni0459", "1114": "uni045A", "1115": "uni045B", "1116": "uni045C", "1117": "uni045D", "1118": "uni045E", "1119": "uni045F", "1120": "uni0460", "1121": "uni0461", "1122": "uni0462", "1123": "uni0463", "1124": "uni0464", "1125": "uni0465", "1126": "uni0466", "1127": "uni0467", "1128": "uni0468", "1129": "uni0469", "1130": "uni046A", "1131": "uni046B", "1132": "uni046C", "1133": "uni046D", "1134": "uni046E", "1135": "uni046F", "1136": "uni0470", "1137": "uni0471", "1138": "uni0472", "1139": "uni0473", "1140": "uni0474", "1141": "uni0475", "1142": "uni0476", "1143": "uni0477", "1144": "uni0478", "1145": "uni0479", "1146": "uni047A", "1147": "uni047B", "1148": "uni047C", "1149": "uni047D", "1150": "uni047E", "1151": "uni047F", "1152": "uni0480", "1153": "uni0481", "1154": "uni0482", "1155": "uni0483", "1156": "uni0484", "1157": "uni0485", "1158": "uni0486", "1160": "uni0488", "1161": "uni0489", "1162": "uni048A", "1163": "uni048B", "1164": "uni048C", "1165": "uni048D", "1166": "uni048E", "1167": "uni048F", "1168": "uni0490", "1169": "uni0491", "1170": "uni0492", "1171": "uni0493", "1172": "uni0494", "1173": "uni0495", "1174": "uni0496", "1175": "uni0497", "1176": "uni0498", "1177": "uni0499", "1178": "uni049A", "1179": "uni049B", "1180": "uni049C", "1181": "uni049D", "1182": "uni049E", "1183": "uni049F", "1184": "uni04A0", "1185": "uni04A1", "1186": "uni04A2", "1187": "uni04A3", "1188": "uni04A4", "1189": "uni04A5", "1190": "uni04A6", "1191": "uni04A7", "1192": "uni04A8", "1193": "uni04A9", "1194": "uni04AA", "1195": "uni04AB", "1196": "uni04AC", "1197": "uni04AD", "1198": "Ustraitcy", "1199": "ustraitcy", "1200": "Ustraitstrokecy", "1201": "ustraitstrokecy", "1202": "uni04B2", "1203": "uni04B3", "1204": "uni04B4", "1205": "uni04B5", "1206": "uni04B6", "1207": "uni04B7", "1208": "uni04B8", "1209": "uni04B9", "1210": "uni04BA", "1211": "uni04BB", "1212": "uni04BC", "1213": "uni04BD", "1214": "uni04BE", "1215": "uni04BF", "1216": "uni04C0", "1217": "uni04C1", "1218": "uni04C2", "1219": "uni04C3", "1220": "uni04C4", "1221": "uni04C5", "1222": "uni04C6", "1223": "uni04C7", "1224": "uni04C8", "1225": "uni04C9", "1226": "uni04CA", "1227": "uni04CB", "1228": "uni04CC", "1229": "uni04CD", "1230": "uni04CE", "1231": "uni04CF", "1232": "uni04D0", "1233": "uni04D1", "1234": "uni04D2", "1235": "uni04D3", "1236": "uni04D4", "1237": "uni04D5", "1238": "uni04D6", "1239": "uni04D7", "1240": "uni04D8", "1241": "uni04D9", "1242": "uni04DA", "1243": "uni04DB", "1244": "uni04DC", "1245": "uni04DD", "1246": "uni04DE", "1247": "uni04DF", "1248": "uni04E0", "1249": "uni04E1", "1250": "uni04E2", "1251": "uni04E3", "1252": "uni04E4", "1253": "uni04E5", "1254": "uni04E6", "1255": "uni04E7", "1256": "uni04E8", "1257": "uni04E9", "1258": "uni04EA", "1259": "uni04EB", "1260": "uni04EC", "1261": "uni04ED", "1262": "uni04EE", "1263": "uni04EF", "1264": "uni04F0", "1265": "uni04F1", "1266": "uni04F2", "1267": "uni04F3", "1268": "uni04F4", "1269": "uni04F5", "1270": "uni04F6", "1271": "uni04F7", "1272": "uni04F8", "1273": "uni04F9", "1274": "uni04FA", "1275": "uni04FB", "1276": "uni04FC", "1277": "uni04FD", "1278": "uni04FE", "1279": "uni04FF", "1280": "uni0500", "1281": "uni0501", "1282": "uni0502", "1283": "uni0503", "1284": "uni0504", "1285": "uni0505", "1286": "uni0506", "1287": "uni0507", "1288": "uni0508", "1289": "uni0509", "1290": "uni050A", "1291": "uni050B", "1292": "uni050C", "1293": "uni050D", "1294": "uni050E", "1295": "uni050F", "1296": "uni0510", "1297": "uni0511", "1298": "uni0512", "1299": "uni0513", "7680": "uni1E00", "7681": "uni1E01", "7742": "uni1E3E", "7743": "uni1E3F", "7808": "<PERSON><PERSON>", "7809": "wgrave", "7810": "Wacute", "7811": "wacute", "7812": "Wdieresis", "7813": "wdieresis", "7838": "uni1E9E", "7840": "uni1EA0", "7841": "uni1EA1", "7842": "uni1EA2", "7843": "uni1EA3", "7844": "uni1EA4", "7845": "uni1EA5", "7846": "uni1EA6", "7847": "uni1EA7", "7848": "uni1EA8", "7849": "uni1EA9", "7850": "uni1EAA", "7851": "uni1EAB", "7852": "uni1EAC", "7853": "uni1EAD", "7854": "uni1EAE", "7855": "uni1EAF", "7856": "uni1EB0", "7857": "uni1EB1", "7858": "uni1EB2", "7859": "uni1EB3", "7860": "uni1EB4", "7861": "uni1EB5", "7862": "uni1EB6", "7863": "uni1EB7", "7864": "uni1EB8", "7865": "uni1EB9", "7866": "uni1EBA", "7867": "uni1EBB", "7868": "uni1EBC", "7869": "uni1EBD", "7870": "uni1EBE", "7871": "uni1EBF", "7872": "uni1EC0", "7873": "uni1EC1", "7874": "uni1EC2", "7875": "uni1EC3", "7876": "uni1EC4", "7877": "uni1EC5", "7878": "uni1EC6", "7879": "uni1EC7", "7880": "uni1EC8", "7881": "uni1EC9", "7882": "uni1ECA", "7883": "uni1ECB", "7884": "uni1ECC", "7885": "uni1ECD", "7886": "uni1ECE", "7887": "uni1ECF", "7888": "uni1ED0", "7889": "uni1ED1", "7890": "uni1ED2", "7891": "uni1ED3", "7892": "uni1ED4", "7893": "uni1ED5", "7894": "uni1ED6", "7895": "uni1ED7", "7896": "uni1ED8", "7897": "uni1ED9", "7898": "uni1EDA", "7899": "uni1EDB", "7900": "uni1EDC", "7901": "uni1EDD", "7902": "uni1EDE", "7903": "uni1EDF", "7904": "uni1EE0", "7905": "uni1EE1", "7906": "uni1EE2", "7907": "uni1EE3", "7908": "uni1EE4", "7909": "uni1EE5", "7910": "uni1EE6", "7911": "uni1EE7", "7912": "uni1EE8", "7913": "uni1EE9", "7914": "uni1EEA", "7915": "uni1EEB", "7916": "uni1EEC", "7917": "uni1EED", "7918": "uni1EEE", "7919": "uni1EEF", "7920": "uni1EF0", "7921": "uni1EF1", "7922": "<PERSON><PERSON>", "7923": "ygrave", "7924": "uni1EF4", "7925": "uni1EF5", "7926": "uni1EF6", "7927": "uni1EF7", "7928": "uni1EF8", "7929": "uni1EF9", "8013": "uni1F4D", "8189": "uni1FFD", "8190": "uni1FFE", "8192": "uni2000", "8193": "uni2001", "8194": "uni2002", "8195": "uni2003", "8196": "uni2004", "8197": "uni2005", "8198": "uni2006", "8199": "uni2007", "8200": "uni2008", "8201": "uni2009", "8202": "uni200A", "8203": "uni200B", "8211": "endash", "8212": "emdash", "8213": "uni2015", "8215": "underscoredbl", "8216": "quoteleft", "8217": "quoteright", "8218": "quotesinglbase", "8219": "quotereversed", "8220": "quotedblleft", "8221": "<PERSON><PERSON><PERSON><PERSON>", "8222": "quotedblbase", "8224": "dagger", "8225": "daggerdbl", "8226": "bullet", "8229": "twodotenleader", "8230": "ellipsis", "8240": "perth<PERSON>and", "8242": "minute", "8243": "second", "8249": "guil<PERSON>lle<PERSON>", "8250": "guil<PERSON><PERSON><PERSON>", "8252": "exclamdbl", "8260": "fraction", "8308": "uni2074", "8319": "uni207F", "8355": "franc", "8356": "lira", "8359": "peseta", "8363": "dong", "8364": "Euro", "8453": "uni2105", "8467": "uni2113", "8470": "uni2116", "8482": "trademark", "8486": "uni03A9", "8494": "estimated", "8539": "oneeighth", "8540": "threeeighths", "8541": "fiveeighths", "8542": "seveneighths", "8706": "<PERSON><PERSON><PERSON>", "8710": "uni0394", "8719": "product", "8721": "summation", "8722": "minus", "8725": "uni2215", "8730": "radical", "8734": "infinity", "8747": "integral", "8776": "approxequal", "8800": "notequal", "8804": "lessequal", "8805": "greaterequal", "9674": "lozenge", "63171": "uni0326.1", "64257": "fi", "64258": "fl", "64259": "uniFB03", "64260": "uniFB04", "65279": "uniFEFF", "65532": "uniFFFC", "65533": "uniFFFD"}, "isUnicode": true, "EncodingScheme": "FontSpecific", "FontName": "<PERSON><PERSON> Slab", "FullName": "Roboto Slab Regular", "Version": "Version 2.002", "PostScriptName": "RobotoSlab-Regular", "Weight": "Medium", "ItalicAngle": "0", "IsFixedPitch": "false", "UnderlineThickness": "49", "UnderlinePosition": "-73", "FontHeightOffset": "0", "Ascender": "1048", "Descender": "-271", "FontBBox": ["-173", "-271", "1232", "1048"], "StartCharMetrics": "1154", "C": {"13": 249, "32": 249, "33": 236, "34": 376, "35": 608, "36": 542, "37": 713, "38": 629, "39": 225, "40": 323, "41": 319, "42": 468, "43": 559, "44": 197, "45": 389, "46": 241, "47": 402, "48": 570, "49": 412, "50": 552, "51": 540, "52": 580, "53": 528, "54": 559, "55": 551, "56": 552, "57": 563, "58": 205, "59": 207, "60": 497, "61": 550, "62": 518, "63": 465, "64": 889, "65": 749, "66": 646, "67": 630, "68": 662, "69": 638, "70": 613, "71": 657, "72": 771, "73": 334, "74": 571, "75": 733, "76": 591, "77": 968, "78": 777, "79": 660, "80": 633, "81": 672, "82": 676, "83": 589, "84": 687, "85": 743, "86": 736, "87": 1040, "88": 747, "89": 713, "90": 584, "91": 280, "92": 411, "93": 270, "94": 422, "95": 570, "96": 242, "97": 553, "98": 559, "99": 521, "100": 595, "101": 517, "102": 354, "103": 565, "104": 642, "105": 318, "106": 256, "107": 628, "108": 319, "109": 965, "110": 656, "111": 549, "112": 584, "113": 556, "114": 417, "115": 499, "116": 356, "117": 608, "118": 589, "119": 890, "120": 587, "121": 582, "122": 504, "123": 342, "124": 220, "125": 342, "126": 677, "160": 249, "161": 236, "162": 546, "163": 576, "164": 713, "165": 677, "166": 242, "167": 602, "168": 347, "169": 779, "170": 437, "171": 457, "172": 548, "174": 779, "175": 391, "176": 370, "177": 516, "178": 404, "179": 413, "180": 242, "181": 642, "182": 532, "183": 252, "184": 229, "185": 267, "186": 459, "187": 458, "188": 705, "189": 723, "190": 825, "191": 435, "192": 749, "193": 749, "194": 749, "195": 749, "196": 749, "197": 749, "198": 1023, "199": 630, "200": 638, "201": 638, "202": 638, "203": 638, "204": 334, "205": 334, "206": 334, "207": 334, "208": 662, "209": 777, "210": 660, "211": 660, "212": 660, "213": 660, "214": 660, "215": 522, "216": 660, "217": 743, "218": 743, "219": 743, "220": 743, "221": 713, "222": 624, "223": 629, "224": 553, "225": 553, "226": 553, "227": 553, "228": 553, "229": 553, "230": 848, "231": 521, "232": 517, "233": 517, "234": 517, "235": 517, "236": 318, "237": 318, "238": 318, "239": 318, "240": 582, "241": 656, "242": 549, "243": 549, "244": 549, "245": 549, "246": 549, "247": 561, "248": 549, "249": 608, "250": 608, "251": 608, "252": 608, "253": 582, "254": 580, "255": 582, "256": 749, "257": 553, "258": 749, "259": 553, "260": 749, "261": 553, "262": 630, "263": 521, "264": 630, "265": 521, "266": 630, "267": 521, "268": 630, "269": 521, "270": 662, "271": 595, "272": 662, "273": 595, "274": 638, "275": 517, "276": 638, "277": 517, "278": 638, "279": 517, "280": 638, "281": 517, "282": 638, "283": 517, "284": 657, "285": 565, "286": 657, "287": 565, "288": 657, "289": 565, "290": 657, "291": 565, "292": 771, "293": 642, "294": 771, "295": 642, "296": 334, "297": 318, "298": 334, "299": 318, "300": 334, "301": 318, "302": 334, "303": 318, "304": 334, "305": 318, "306": 901, "307": 607, "308": 571, "309": 256, "310": 733, "311": 628, "312": 614, "313": 591, "314": 319, "315": 591, "316": 319, "317": 591, "318": 319, "319": 591, "320": 319, "321": 591, "322": 319, "323": 777, "324": 656, "325": 777, "326": 656, "327": 777, "328": 656, "329": 656, "330": 761, "331": 617, "332": 660, "333": 549, "334": 660, "335": 549, "336": 660, "337": 549, "338": 970, "339": 901, "340": 676, "341": 417, "342": 676, "343": 417, "344": 676, "345": 417, "346": 589, "347": 499, "348": 589, "349": 499, "350": 589, "351": 499, "352": 589, "353": 499, "354": 687, "355": 356, "356": 687, "357": 356, "358": 687, "359": 356, "360": 743, "361": 608, "362": 743, "363": 608, "364": 743, "365": 608, "366": 743, "367": 608, "368": 743, "369": 608, "370": 743, "371": 608, "372": 1040, "373": 890, "374": 713, "375": 582, "376": 713, "377": 584, "378": 504, "379": 584, "380": 504, "381": 584, "382": 504, "383": 358, "399": 685, "402": 375, "416": 660, "417": 568, "431": 842, "432": 654, "496": 256, "506": 749, "507": 553, "508": 1023, "509": 848, "510": 660, "511": 549, "536": 589, "537": 499, "538": 687, "539": 356, "567": 256, "601": 544, "700": 204, "710": 338, "711": 338, "713": 391, "728": 343, "729": 140, "730": 241, "731": 233, "732": 377, "733": 401, "755": 231, "768": 0, "769": 0, "770": 0, "771": 0, "772": 0, "774": 0, "775": 0, "776": 0, "777": 0, "778": 0, "779": 0, "780": 0, "783": 0, "786": 0, "795": 0, "803": 0, "805": 0, "806": 0, "807": 0, "808": 0, "821": 0, "822": 0, "823": 0, "824": 0, "900": 241, "901": 374, "902": 749, "903": 252, "904": 731, "905": 874, "906": 438, "908": 660, "910": 816, "911": 665, "912": 327, "913": 749, "914": 646, "915": 594, "916": 699, "917": 638, "918": 584, "919": 771, "920": 685, "921": 334, "922": 733, "923": 708, "924": 968, "925": 777, "926": 576, "927": 660, "928": 770, "929": 633, "931": 577, "932": 687, "933": 713, "934": 718, "935": 686, "936": 813, "937": 661, "938": 334, "939": 713, "940": 590, "941": 536, "942": 580, "943": 327, "944": 596, "945": 590, "946": 572, "947": 530, "948": 568, "949": 536, "950": 522, "951": 580, "952": 567, "953": 327, "954": 614, "955": 554, "956": 642, "957": 558, "958": 490, "959": 561, "960": 625, "961": 564, "962": 533, "963": 608, "964": 495, "965": 596, "966": 732, "967": 582, "968": 770, "969": 820, "970": 327, "971": 596, "972": 561, "973": 596, "974": 820, "977": 655, "978": 601, "982": 816, "1024": 638, "1025": 638, "1026": 768, "1027": 594, "1028": 627, "1029": 589, "1030": 323, "1031": 334, "1032": 578, "1033": 1077, "1034": 1110, "1035": 852, "1036": 709, "1037": 769, "1038": 664, "1039": 769, "1040": 749, "1041": 664, "1042": 657, "1043": 594, "1044": 767, "1045": 638, "1046": 997, "1047": 597, "1048": 769, "1049": 769, "1050": 709, "1051": 738, "1052": 968, "1053": 771, "1054": 706, "1055": 770, "1056": 635, "1057": 645, "1058": 687, "1059": 664, "1060": 781, "1061": 747, "1062": 778, "1063": 745, "1064": 995, "1065": 998, "1066": 850, "1067": 924, "1068": 663, "1069": 614, "1070": 974, "1071": 676, "1072": 553, "1073": 569, "1074": 621, "1075": 525, "1076": 637, "1077": 517, "1078": 844, "1079": 512, "1080": 682, "1081": 682, "1082": 617, "1083": 643, "1084": 842, "1085": 681, "1086": 561, "1087": 682, "1088": 605, "1089": 521, "1090": 579, "1091": 582, "1092": 718, "1093": 587, "1094": 665, "1095": 662, "1096": 914, "1097": 915, "1098": 693, "1099": 846, "1100": 592, "1101": 532, "1102": 862, "1103": 636, "1104": 517, "1105": 517, "1106": 634, "1107": 525, "1108": 546, "1109": 499, "1110": 329, "1111": 318, "1112": 279, "1113": 872, "1114": 925, "1115": 642, "1116": 617, "1117": 682, "1118": 582, "1119": 682, "1120": 861, "1121": 784, "1122": 663, "1123": 588, "1124": 881, "1125": 804, "1126": 697, "1127": 587, "1128": 945, "1129": 859, "1130": 925, "1131": 781, "1132": 1190, "1133": 1068, "1134": 514, "1135": 462, "1136": 813, "1137": 770, "1138": 685, "1139": 579, "1140": 677, "1141": 568, "1142": 677, "1143": 568, "1144": 1242, "1145": 1126, "1146": 706, "1147": 569, "1148": 851, "1149": 748, "1150": 861, "1151": 764, "1152": 619, "1153": 517, "1154": 556, "1155": 0, "1156": 0, "1157": 0, "1158": 0, "1160": 984, "1161": 945, "1162": 769, "1163": 682, "1164": 663, "1165": 585, "1166": 635, "1167": 605, "1168": 589, "1169": 524, "1170": 594, "1171": 525, "1172": 605, "1173": 560, "1174": 997, "1175": 844, "1176": 597, "1177": 512, "1178": 709, "1179": 617, "1180": 718, "1181": 668, "1182": 709, "1183": 618, "1184": 873, "1185": 764, "1186": 771, "1187": 681, "1188": 999, "1189": 774, "1190": 1039, "1191": 932, "1192": 752, "1193": 624, "1194": 645, "1195": 521, "1196": 687, "1197": 579, "1198": 713, "1199": 530, "1200": 713, "1201": 530, "1202": 747, "1203": 587, "1204": 887, "1205": 769, "1206": 745, "1207": 662, "1208": 746, "1209": 660, "1210": 750, "1211": 642, "1212": 751, "1213": 640, "1214": 751, "1215": 640, "1216": 323, "1217": 997, "1218": 844, "1219": 696, "1220": 624, "1221": 738, "1222": 643, "1223": 763, "1224": 668, "1225": 771, "1226": 681, "1227": 745, "1228": 662, "1229": 968, "1230": 842, "1231": 323, "1232": 749, "1233": 553, "1234": 749, "1235": 553, "1236": 1023, "1237": 848, "1238": 638, "1239": 517, "1240": 685, "1241": 544, "1242": 685, "1243": 544, "1244": 997, "1245": 844, "1246": 597, "1247": 512, "1248": 549, "1249": 551, "1250": 769, "1251": 682, "1252": 769, "1253": 682, "1254": 706, "1255": 561, "1256": 685, "1257": 579, "1258": 685, "1259": 579, "1260": 614, "1261": 532, "1262": 664, "1263": 582, "1264": 664, "1265": 582, "1266": 664, "1267": 582, "1268": 745, "1269": 662, "1270": 594, "1271": 525, "1272": 924, "1273": 846, "1274": 594, "1275": 525, "1276": 747, "1277": 587, "1278": 747, "1279": 587, "1280": 659, "1281": 600, "1282": 832, "1283": 844, "1284": 766, "1285": 705, "1286": 542, "1287": 500, "1288": 970, "1289": 833, "1290": 1008, "1291": 898, "1292": 616, "1293": 522, "1294": 724, "1295": 646, "1296": 632, "1297": 536, "1298": 738, "1299": 643, "7680": 749, "7681": 553, "7742": 968, "7743": 965, "7808": 1040, "7809": 890, "7810": 1040, "7811": 890, "7812": 1040, "7813": 890, "7838": 694, "7840": 749, "7841": 553, "7842": 749, "7843": 553, "7844": 749, "7845": 553, "7846": 749, "7847": 553, "7848": 749, "7849": 553, "7850": 749, "7851": 553, "7852": 749, "7853": 553, "7854": 749, "7855": 553, "7856": 749, "7857": 553, "7858": 749, "7859": 553, "7860": 749, "7861": 553, "7862": 749, "7863": 553, "7864": 638, "7865": 517, "7866": 638, "7867": 517, "7868": 638, "7869": 517, "7870": 638, "7871": 517, "7872": 638, "7873": 517, "7874": 638, "7875": 517, "7876": 638, "7877": 517, "7878": 638, "7879": 517, "7880": 334, "7881": 318, "7882": 334, "7883": 318, "7884": 660, "7885": 549, "7886": 660, "7887": 549, "7888": 660, "7889": 549, "7890": 660, "7891": 549, "7892": 660, "7893": 549, "7894": 660, "7895": 549, "7896": 660, "7897": 549, "7898": 660, "7899": 568, "7900": 660, "7901": 568, "7902": 660, "7903": 568, "7904": 660, "7905": 568, "7906": 660, "7907": 568, "7908": 743, "7909": 608, "7910": 743, "7911": 608, "7912": 842, "7913": 654, "7914": 842, "7915": 654, "7916": 842, "7917": 654, "7918": 842, "7919": 654, "7920": 842, "7921": 654, "7922": 713, "7923": 582, "7924": 713, "7925": 582, "7926": 713, "7927": 582, "7928": 713, "7929": 582, "8013": 660, "8189": 241, "8190": 161, "8192": 510, "8193": 1020, "8194": 510, "8195": 1020, "8196": 340, "8197": 255, "8198": 170, "8199": 562, "8200": 273, "8201": 204, "8202": 102, "8203": 0, "8211": 684, "8212": 788, "8213": 788, "8215": 568, "8216": 204, "8217": 204, "8218": 197, "8219": 244, "8220": 363, "8221": 367, "8222": 347, "8224": 549, "8225": 556, "8226": 337, "8229": 457, "8230": 657, "8240": 959, "8242": 225, "8243": 374, "8249": 290, "8250": 290, "8252": 472, "8260": 439, "8308": 445, "8319": 459, "8355": 613, "8356": 587, "8359": 839, "8363": 615, "8364": 516, "8453": 700, "8467": 454, "8470": 1139, "8482": 613, "8486": 661, "8494": 585, "8539": 803, "8540": 875, "8541": 901, "8542": 818, "8706": 582, "8710": 699, "8719": 755, "8721": 605, "8722": 558, "8725": 439, "8730": 603, "8734": 1026, "8747": 332, "8776": 580, "8800": 509, "8804": 513, "8805": 523, "9674": 513, "63171": 0, "64257": 605, "64258": 659, "64259": 960, "64260": 1013, "65279": 0, "65532": 1087, "65533": 966}, "CIDtoGID_Compressed": true, "CIDtoGID": "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", "_version_": 6}
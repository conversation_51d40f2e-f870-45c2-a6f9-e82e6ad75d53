<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class EventDate extends Model
{

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'date',
        'start_at',
        'end_at',
        'created_by',
        'edited_by'
    ];

    public function event(): BelongsTo
    {
        return $this->belongsTo(Event::class);
    }
}

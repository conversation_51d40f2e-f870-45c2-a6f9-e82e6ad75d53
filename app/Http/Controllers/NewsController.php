<?php

namespace App\Http\Controllers;

use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\File;


class NewsController extends Controller
{

    public function index(Request $request)
    {
        // Get the news data from the combined JSON file
        $jsonData = $this->getJsonData();

        // Get the filter type from the request, default to 'all'
        $filter = $request->query('filter', 'all');

        // Filter the data based on the filter type
        $data = $this->filterData($jsonData, $filter);

        // Sort the data by date (newest first)
        $data = $this->sortByDate($data);

        return view('front-end.pages.news', [
            'items' => $data,
            'filter' => $filter,
            'contactPerson' => $this->getContactPerson()
        ]);
    }

    public function show($slug) {
        // Get related news items for the more-news section
        $relatedNews = $this->getRelatedNews($slug, 4);

        return view('front-end.pages.news.'.locale().'.'.$slug, [
            'relatedNews' => $relatedNews
        ]);
    }


    /**
     * Get the data from the combined JSON file.
     *
     * @return array
     */
    private function getJsonData()
    {
        return json_decode(file_get_contents('json/global-data.json'), true);
    }

    /**
     * Filter the data based on the filter type.
     *
     * @param array $data
     * @param string $filter
     * @return array
     */
    private function filterData($data, $filter)
    {
        if ($filter === 'all') {
            return $data;
        }

        return array_filter($data, function ($item) use ($filter) {
            return $item['type'] === $filter;
        });
    }

    /**
     * Sort the data by date (newest first).
     *
     * @param array $data
     * @return array
     */
    private function sortByDate($data)
    {
        usort($data, function ($a, $b) {
            return strtotime($b['date']) - strtotime($a['date']);
        });

        return $data;
    }

    /**
     * Get the contact person data.
     *
     * @return array
     */
    private function getContactPerson()
    {
        return [
            'name' => 'Lubomíra Černá',
            'position' => 'PR a marketing manager',
            'email' => '<EMAIL>',
            'phone' => '+420 737 281 550'
        ];
    }

    /**
     * Get related news items excluding the current one.
     *
     * @param string $currentSlug
     * @param int $limit
     * @return array
     */
    public function getRelatedNews($currentSlug, $limit = 4)
    {
        // Get all news data
        $allNews = $this->getJsonData();

        // Filter out the current news item
        $filteredNews = array_filter($allNews, function($item) use ($currentSlug) {
            $slugEn = $item['slug_en'] ?? '';
            $slugCs = $item['slug_cs'] ?? '';
            return $slugEn !== $currentSlug && $slugCs !== $currentSlug;
        });

        // Sort by date
        $sortedNews = $this->sortByDate($filteredNews);

        // Take only the specified number of items
        return array_slice($sortedNews, 0, $limit);
    }
}

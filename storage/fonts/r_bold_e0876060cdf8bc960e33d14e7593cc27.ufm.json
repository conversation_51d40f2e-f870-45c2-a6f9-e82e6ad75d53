{"codeToName": {"13": "CR", "32": "space", "33": "exclam", "34": "quotedbl", "35": "numbersign", "36": "dollar", "37": "percent", "38": "ampersand", "39": "<PERSON><PERSON><PERSON>", "40": "parenleft", "41": "parenright", "42": "asterisk", "43": "plus", "44": "comma", "45": "hyphen", "46": "period", "47": "slash", "48": "zero", "49": "one", "50": "two", "51": "three", "52": "four", "53": "five", "54": "six", "55": "seven", "56": "eight", "57": "nine", "58": "colon", "59": "semicolon", "60": "less", "61": "equal", "62": "greater", "63": "question", "64": "at", "65": "A", "66": "B", "67": "C", "68": "D", "69": "E", "70": "F", "71": "G", "72": "H", "73": "I", "74": "J", "75": "K", "76": "L", "77": "M", "78": "N", "79": "O", "80": "P", "81": "Q", "82": "R", "83": "S", "84": "T", "85": "U", "86": "V", "87": "W", "88": "X", "89": "Y", "90": "Z", "91": "bracketleft", "92": "backslash", "93": "bracketright", "94": "asciicircum", "95": "underscore", "96": "grave", "97": "a", "98": "b", "99": "c", "100": "d", "101": "e", "102": "f", "103": "g", "104": "h", "105": "i", "106": "j", "107": "k", "108": "l", "109": "m", "110": "n", "111": "o", "112": "p", "113": "q", "114": "r", "115": "s", "116": "t", "117": "u", "118": "v", "119": "w", "120": "x", "121": "y", "122": "z", "123": "braceleft", "124": "bar", "125": "braceright", "126": "asciitilde", "160": "uni00A0", "161": "exclamdown", "162": "cent", "163": "sterling", "164": "currency", "165": "yen", "166": "brokenbar", "167": "section", "168": "<PERSON><PERSON><PERSON>", "169": "copyright", "170": "ordfeminine", "171": "guillemotleft", "172": "logicalnot", "174": "registered", "175": "macron", "176": "degree", "177": "plus<PERSON>us", "178": "uni00B2", "179": "uni00B3", "180": "acute", "181": "uni00B5", "182": "paragraph", "183": "periodcentered", "184": "cedilla", "185": "uni00B9", "186": "ordmasculine", "187": "guil<PERSON><PERSON><PERSON>", "188": "onequarter", "189": "onehalf", "190": "threequarters", "191": "questiondown", "192": "<PERSON><PERSON>", "193": "Aacute", "194": "Acircumflex", "195": "<PERSON><PERSON>", "196": "Adieresis", "197": "<PERSON><PERSON>", "198": "AE", "199": "Ccedilla", "200": "<PERSON><PERSON>", "201": "Eacute", "202": "Ecircumflex", "203": "Edieresis", "204": "<PERSON><PERSON>", "205": "Iacute", "206": "Icircumflex", "207": "Idieresis", "208": "Eth", "209": "Ntilde", "210": "<PERSON><PERSON>", "211": "Oacute", "212": "Ocircumflex", "213": "<PERSON><PERSON><PERSON>", "214": "Odieresis", "215": "multiply", "216": "<PERSON><PERSON><PERSON>", "217": "<PERSON><PERSON>", "218": "Uacute", "219": "Ucircumflex", "220": "Udieresis", "221": "Ya<PERSON>", "222": "Thorn", "223": "germandbls", "224": "agrave", "225": "aacute", "226": "acircumflex", "227": "atilde", "228": "adieresis", "229": "aring", "230": "ae", "231": "ccedilla", "232": "egrave", "233": "eacute", "234": "ecircumflex", "235": "edieresis", "236": "igrave", "237": "iacute", "238": "icircumflex", "239": "idieresis", "240": "eth", "241": "ntilde", "242": "ograve", "243": "oacute", "244": "ocircumflex", "245": "otilde", "246": "odieresis", "247": "divide", "248": "oslash", "249": "ugrave", "250": "uacute", "251": "ucircumflex", "252": "udieresis", "253": "yacute", "254": "thorn", "255": "ydieresis", "256": "Amacron", "257": "amacron", "258": "Abreve", "259": "abreve", "260": "Aogonek", "261": "aogonek", "262": "Cacute", "263": "cacute", "264": "Ccircumflex", "265": "ccircumflex", "266": "Cdotaccent", "267": "cdotaccent", "268": "<PERSON><PERSON><PERSON>", "269": "ccaron", "270": "<PERSON><PERSON><PERSON>", "271": "dcaron", "272": "Dcroat", "273": "dmacron", "274": "Emacron", "275": "emacron", "276": "Ebreve", "277": "ebreve", "278": "Edotaccent", "279": "edotaccent", "280": "Eogonek", "281": "eogonek", "282": "<PERSON><PERSON><PERSON>", "283": "ecaron", "284": "Gcircumflex", "285": "gcircumflex", "286": "Gbreve", "287": "gbreve", "288": "Gdotaccent", "289": "gdotaccent", "290": "uni0122", "291": "uni0123", "292": "Hcircumflex", "293": "hcircumflex", "294": "H<PERSON>", "295": "hbar", "296": "Itilde", "297": "itilde", "298": "Imacron", "299": "imacron", "300": "Ibreve", "301": "i<PERSON>ve", "302": "Iogonek", "303": "iogonek", "304": "Idot", "305": "dotlessi", "306": "IJ", "307": "ij", "308": "Jcircumflex", "309": "jcircumflex", "310": "uni0136", "311": "uni0137", "312": "kgreenlandic", "313": "<PERSON><PERSON>", "314": "lacute", "315": "uni013B", "316": "uni013C", "317": "<PERSON><PERSON><PERSON>", "318": "lcaron", "319": "Ldot", "320": "ldot", "321": "Lslash", "322": "lslash", "323": "Nacute", "324": "nacute", "325": "uni0145", "326": "uni0146", "327": "<PERSON><PERSON><PERSON>", "328": "ncaron", "329": "napostrophe", "330": "Eng", "331": "eng", "332": "Omacron", "333": "omacron", "334": "Obreve", "335": "obreve", "336": "Ohungarumlau<PERSON>", "337": "ohungarumlaut", "338": "OE", "339": "oe", "340": "<PERSON><PERSON>", "341": "racute", "342": "uni0156", "343": "uni0157", "344": "<PERSON><PERSON><PERSON>", "345": "rcaron", "346": "Sacute", "347": "sacute", "348": "Scircumflex", "349": "scircumflex", "350": "Scedilla", "351": "scedilla", "352": "<PERSON><PERSON><PERSON>", "353": "scaron", "354": "uni0162", "355": "uni0163", "356": "<PERSON><PERSON><PERSON>", "357": "tcaron", "358": "Tbar", "359": "tbar", "360": "Utilde", "361": "utilde", "362": "Umacron", "363": "umacron", "364": "Ubreve", "365": "ubreve", "366": "<PERSON><PERSON>", "367": "uring", "368": "Uhungaru<PERSON>lau<PERSON>", "369": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "370": "Uogonek", "371": "uogonek", "372": "Wcircumflex", "373": "wcircumflex", "374": "Ycircumflex", "375": "ycircumflex", "376": "Ydieresis", "377": "Zacute", "378": "zacute", "379": "Zdotaccent", "380": "zdotaccent", "381": "<PERSON><PERSON><PERSON>", "382": "z<PERSON>on", "383": "longs", "399": "uni018F", "402": "florin", "416": "<PERSON><PERSON>", "417": "ohorn", "431": "<PERSON><PERSON>", "432": "uhorn", "496": "uni01F0", "506": "Aringacute", "507": "aring<PERSON><PERSON>", "508": "AEacute", "509": "aeacute", "510": "Oslashacute", "511": "oslashacute", "536": "uni0218", "537": "uni0219", "538": "uni021A", "539": "uni021B", "567": "uni0237", "601": "uni0259", "700": "uni02BC", "710": "circumflex", "711": "caron", "713": "uni02C9", "728": "breve", "729": "dotaccent", "730": "ring", "731": "ogonek", "732": "tilde", "733": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "755": "uni02F3", "768": "gravecomb", "769": "acutecomb", "770": "uni0302", "771": "tildecomb", "772": "uni0304", "774": "uni0306", "775": "uni0307", "776": "uni0308", "777": "hookab<PERSON><PERSON>", "778": "uni030A", "779": "uni030B", "780": "uni030C", "783": "uni030F", "786": "uni0312", "795": "uni031B", "803": "dotbelowcomb", "805": "uni0325", "806": "uni0326", "807": "uni0327", "808": "uni0328", "821": "uni0335", "822": "uni0336", "823": "uni0337", "824": "uni0338", "900": "tonos", "901": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "902": "Alphatonos", "903": "anoteleia", "904": "Epsilontonos", "905": "Etatonos", "906": "Iotatonos", "908": "Omicrontonos", "910": "Upsilontonos", "911": "Omegatonos", "912": "iotadieresistonos", "913": "Alpha", "914": "Beta", "915": "Gamma", "916": "uni0394", "917": "Epsilon", "918": "Zeta", "919": "Eta", "920": "Theta", "921": "Iota", "922": "Kappa", "923": "Lambda", "924": "Mu", "925": "<PERSON>u", "926": "Xi", "927": "Omicron", "928": "Pi", "929": "Rho", "931": "Sigma", "932": "Tau", "933": "Upsilon", "934": "Phi", "935": "<PERSON>", "936": "Psi", "937": "uni03A9", "938": "Iotadieresis", "939": "Upsilondieresis", "940": "alphatonos", "941": "epsilontonos", "942": "etatonos", "943": "iotatonos", "944": "upsilondieresistonos", "945": "alpha", "946": "beta", "947": "gamma", "948": "delta", "949": "epsilon", "950": "zeta", "951": "eta", "952": "theta", "953": "iota", "954": "kappa", "955": "lambda", "956": "uni03BC", "957": "nu", "958": "xi", "959": "omicron", "960": "pi", "961": "rho", "962": "uni03C2", "963": "sigma", "964": "tau", "965": "upsilon", "966": "phi", "967": "chi", "968": "psi", "969": "omega", "970": "iotadieresis", "971": "upsilondieresis", "972": "omicrontonos", "973": "upsilontonos", "974": "omegatonos", "977": "uni03D1", "978": "uni03D2", "982": "uni03D6", "1024": "uni0400", "1025": "uni0401", "1026": "uni0402", "1027": "uni0403", "1028": "uni0404", "1029": "uni0405", "1030": "uni0406", "1031": "uni0407", "1032": "uni0408", "1033": "uni0409", "1034": "uni040A", "1035": "uni040B", "1036": "uni040C", "1037": "uni040D", "1038": "uni040E", "1039": "uni040F", "1040": "uni0410", "1041": "uni0411", "1042": "uni0412", "1043": "uni0413", "1044": "uni0414", "1045": "uni0415", "1046": "uni0416", "1047": "uni0417", "1048": "uni0418", "1049": "uni0419", "1050": "uni041A", "1051": "uni041B", "1052": "uni041C", "1053": "uni041D", "1054": "uni041E", "1055": "uni041F", "1056": "uni0420", "1057": "uni0421", "1058": "uni0422", "1059": "uni0423", "1060": "uni0424", "1061": "uni0425", "1062": "uni0426", "1063": "uni0427", "1064": "uni0428", "1065": "uni0429", "1066": "uni042A", "1067": "uni042B", "1068": "uni042C", "1069": "uni042D", "1070": "uni042E", "1071": "uni042F", "1072": "uni0430", "1073": "uni0431", "1074": "uni0432", "1075": "uni0433", "1076": "uni0434", "1077": "uni0435", "1078": "uni0436", "1079": "uni0437", "1080": "uni0438", "1081": "uni0439", "1082": "uni043A", "1083": "uni043B", "1084": "uni043C", "1085": "uni043D", "1086": "uni043E", "1087": "uni043F", "1088": "uni0440", "1089": "uni0441", "1090": "uni0442", "1091": "uni0443", "1092": "uni0444", "1093": "uni0445", "1094": "uni0446", "1095": "uni0447", "1096": "uni0448", "1097": "uni0449", "1098": "uni044A", "1099": "uni044B", "1100": "uni044C", "1101": "uni044D", "1102": "uni044E", "1103": "uni044F", "1104": "uni0450", "1105": "uni0451", "1106": "uni0452", "1107": "uni0453", "1108": "uni0454", "1109": "uni0455", "1110": "uni0456", "1111": "uni0457", "1112": "uni0458", "1113": "uni0459", "1114": "uni045A", "1115": "uni045B", "1116": "uni045C", "1117": "uni045D", "1118": "uni045E", "1119": "uni045F", "1120": "uni0460", "1121": "uni0461", "1122": "uni0462", "1123": "uni0463", "1124": "uni0464", "1125": "uni0465", "1126": "uni0466", "1127": "uni0467", "1128": "uni0468", "1129": "uni0469", "1130": "uni046A", "1131": "uni046B", "1132": "uni046C", "1133": "uni046D", "1134": "uni046E", "1135": "uni046F", "1136": "uni0470", "1137": "uni0471", "1138": "uni0472", "1139": "uni0473", "1140": "uni0474", "1141": "uni0475", "1142": "uni0476", "1143": "uni0477", "1144": "uni0478", "1145": "uni0479", "1146": "uni047A", "1147": "uni047B", "1148": "uni047C", "1149": "uni047D", "1150": "uni047E", "1151": "uni047F", "1152": "uni0480", "1153": "uni0481", "1154": "uni0482", "1155": "uni0483", "1156": "uni0484", "1157": "uni0485", "1158": "uni0486", "1160": "uni0488", "1161": "uni0489", "1162": "uni048A", "1163": "uni048B", "1164": "uni048C", "1165": "uni048D", "1166": "uni048E", "1167": "uni048F", "1168": "uni0490", "1169": "uni0491", "1170": "uni0492", "1171": "uni0493", "1172": "uni0494", "1173": "uni0495", "1174": "uni0496", "1175": "uni0497", "1176": "uni0498", "1177": "uni0499", "1178": "uni049A", "1179": "uni049B", "1180": "uni049C", "1181": "uni049D", "1182": "uni049E", "1183": "uni049F", "1184": "uni04A0", "1185": "uni04A1", "1186": "uni04A2", "1187": "uni04A3", "1188": "uni04A4", "1189": "uni04A5", "1190": "uni04A6", "1191": "uni04A7", "1192": "uni04A8", "1193": "uni04A9", "1194": "uni04AA", "1195": "uni04AB", "1196": "uni04AC", "1197": "uni04AD", "1198": "Ustraitcy", "1199": "ustraitcy", "1200": "Ustraitstrokecy", "1201": "ustraitstrokecy", "1202": "uni04B2", "1203": "uni04B3", "1204": "uni04B4", "1205": "uni04B5", "1206": "uni04B6", "1207": "uni04B7", "1208": "uni04B8", "1209": "uni04B9", "1210": "uni04BA", "1211": "uni04BB", "1212": "uni04BC", "1213": "uni04BD", "1214": "uni04BE", "1215": "uni04BF", "1216": "uni04C0", "1217": "uni04C1", "1218": "uni04C2", "1219": "uni04C3", "1220": "uni04C4", "1221": "uni04C5", "1222": "uni04C6", "1223": "uni04C7", "1224": "uni04C8", "1225": "uni04C9", "1226": "uni04CA", "1227": "uni04CB", "1228": "uni04CC", "1229": "uni04CD", "1230": "uni04CE", "1231": "uni04CF", "1232": "uni04D0", "1233": "uni04D1", "1234": "uni04D2", "1235": "uni04D3", "1236": "uni04D4", "1237": "uni04D5", "1238": "uni04D6", "1239": "uni04D7", "1240": "uni04D8", "1241": "uni04D9", "1242": "uni04DA", "1243": "uni04DB", "1244": "uni04DC", "1245": "uni04DD", "1246": "uni04DE", "1247": "uni04DF", "1248": "uni04E0", "1249": "uni04E1", "1250": "uni04E2", "1251": "uni04E3", "1252": "uni04E4", "1253": "uni04E5", "1254": "uni04E6", "1255": "uni04E7", "1256": "uni04E8", "1257": "uni04E9", "1258": "uni04EA", "1259": "uni04EB", "1260": "uni04EC", "1261": "uni04ED", "1262": "uni04EE", "1263": "uni04EF", "1264": "uni04F0", "1265": "uni04F1", "1266": "uni04F2", "1267": "uni04F3", "1268": "uni04F4", "1269": "uni04F5", "1270": "uni04F6", "1271": "uni04F7", "1272": "uni04F8", "1273": "uni04F9", "1274": "uni04FA", "1275": "uni04FB", "1276": "uni04FC", "1277": "uni04FD", "1278": "uni04FE", "1279": "uni04FF", "1280": "uni0500", "1281": "uni0501", "1282": "uni0502", "1283": "uni0503", "1284": "uni0504", "1285": "uni0505", "1286": "uni0506", "1287": "uni0507", "1288": "uni0508", "1289": "uni0509", "1290": "uni050A", "1291": "uni050B", "1292": "uni050C", "1293": "uni050D", "1294": "uni050E", "1295": "uni050F", "1296": "uni0510", "1297": "uni0511", "1298": "uni0512", "1299": "uni0513", "7680": "uni1E00", "7681": "uni1E01", "7742": "uni1E3E", "7743": "uni1E3F", "7808": "<PERSON><PERSON>", "7809": "wgrave", "7810": "Wacute", "7811": "wacute", "7812": "Wdieresis", "7813": "wdieresis", "7838": "uni1E9E", "7840": "uni1EA0", "7841": "uni1EA1", "7842": "uni1EA2", "7843": "uni1EA3", "7844": "uni1EA4", "7845": "uni1EA5", "7846": "uni1EA6", "7847": "uni1EA7", "7848": "uni1EA8", "7849": "uni1EA9", "7850": "uni1EAA", "7851": "uni1EAB", "7852": "uni1EAC", "7853": "uni1EAD", "7854": "uni1EAE", "7855": "uni1EAF", "7856": "uni1EB0", "7857": "uni1EB1", "7858": "uni1EB2", "7859": "uni1EB3", "7860": "uni1EB4", "7861": "uni1EB5", "7862": "uni1EB6", "7863": "uni1EB7", "7864": "uni1EB8", "7865": "uni1EB9", "7866": "uni1EBA", "7867": "uni1EBB", "7868": "uni1EBC", "7869": "uni1EBD", "7870": "uni1EBE", "7871": "uni1EBF", "7872": "uni1EC0", "7873": "uni1EC1", "7874": "uni1EC2", "7875": "uni1EC3", "7876": "uni1EC4", "7877": "uni1EC5", "7878": "uni1EC6", "7879": "uni1EC7", "7880": "uni1EC8", "7881": "uni1EC9", "7882": "uni1ECA", "7883": "uni1ECB", "7884": "uni1ECC", "7885": "uni1ECD", "7886": "uni1ECE", "7887": "uni1ECF", "7888": "uni1ED0", "7889": "uni1ED1", "7890": "uni1ED2", "7891": "uni1ED3", "7892": "uni1ED4", "7893": "uni1ED5", "7894": "uni1ED6", "7895": "uni1ED7", "7896": "uni1ED8", "7897": "uni1ED9", "7898": "uni1EDA", "7899": "uni1EDB", "7900": "uni1EDC", "7901": "uni1EDD", "7902": "uni1EDE", "7903": "uni1EDF", "7904": "uni1EE0", "7905": "uni1EE1", "7906": "uni1EE2", "7907": "uni1EE3", "7908": "uni1EE4", "7909": "uni1EE5", "7910": "uni1EE6", "7911": "uni1EE7", "7912": "uni1EE8", "7913": "uni1EE9", "7914": "uni1EEA", "7915": "uni1EEB", "7916": "uni1EEC", "7917": "uni1EED", "7918": "uni1EEE", "7919": "uni1EEF", "7920": "uni1EF0", "7921": "uni1EF1", "7922": "<PERSON><PERSON>", "7923": "ygrave", "7924": "uni1EF4", "7925": "uni1EF5", "7926": "uni1EF6", "7927": "uni1EF7", "7928": "uni1EF8", "7929": "uni1EF9", "8013": "uni1F4D", "8189": "uni1FFD", "8190": "uni1FFE", "8192": "uni2000", "8193": "uni2001", "8194": "uni2002", "8195": "uni2003", "8196": "uni2004", "8197": "uni2005", "8198": "uni2006", "8199": "uni2007", "8200": "uni2008", "8201": "uni2009", "8202": "uni200A", "8203": "uni200B", "8211": "endash", "8212": "emdash", "8213": "uni2015", "8215": "underscoredbl", "8216": "quoteleft", "8217": "quoteright", "8218": "quotesinglbase", "8219": "quotereversed", "8220": "quotedblleft", "8221": "<PERSON><PERSON><PERSON><PERSON>", "8222": "quotedblbase", "8224": "dagger", "8225": "daggerdbl", "8226": "bullet", "8229": "twodotenleader", "8230": "ellipsis", "8240": "perth<PERSON>and", "8242": "minute", "8243": "second", "8249": "guil<PERSON>lle<PERSON>", "8250": "guil<PERSON><PERSON><PERSON>", "8252": "exclamdbl", "8260": "fraction", "8308": "uni2074", "8319": "uni207F", "8355": "franc", "8356": "lira", "8359": "peseta", "8363": "dong", "8364": "Euro", "8453": "uni2105", "8467": "uni2113", "8470": "uni2116", "8482": "trademark", "8486": "uni03A9", "8494": "estimated", "8539": "oneeighth", "8540": "threeeighths", "8541": "fiveeighths", "8542": "seveneighths", "8706": "<PERSON><PERSON><PERSON>", "8710": "uni0394", "8719": "product", "8721": "summation", "8722": "minus", "8725": "uni2215", "8730": "radical", "8734": "infinity", "8747": "integral", "8776": "approxequal", "8800": "notequal", "8804": "lessequal", "8805": "greaterequal", "9674": "lozenge", "63171": "uni0326.1", "64257": "fi", "64258": "fl", "64259": "uniFB03", "64260": "uniFB04", "65279": "uniFEFF", "65532": "uniFFFC", "65533": "uniFFFD"}, "isUnicode": true, "EncodingScheme": "FontSpecific", "FontName": "<PERSON><PERSON> Slab", "FullName": "Roboto Slab Bold", "Version": "Version 2.002", "PostScriptName": "RobotoSlab-Bold", "Weight": "Bold", "ItalicAngle": "0", "IsFixedPitch": "false", "UnderlineThickness": "49", "UnderlinePosition": "-73", "FontHeightOffset": "0", "Ascender": "1048", "Descender": "-271", "FontBBox": ["-183", "-271", "1325", "1048"], "StartCharMetrics": "1154", "C": {"13": 245, "32": 245, "33": 248, "34": 384, "35": 585, "36": 541, "37": 698, "38": 624, "39": 219, "40": 334, "41": 348, "42": 446, "43": 536, "44": 246, "45": 398, "46": 255, "47": 389, "48": 571, "49": 442, "50": 555, "51": 543, "52": 565, "53": 533, "54": 559, "55": 541, "56": 544, "57": 557, "58": 222, "59": 221, "60": 494, "61": 548, "62": 503, "63": 481, "64": 882, "65": 738, "66": 656, "67": 645, "68": 696, "69": 639, "70": 618, "71": 670, "72": 781, "73": 349, "74": 587, "75": 754, "76": 586, "77": 1009, "78": 783, "79": 706, "80": 647, "81": 708, "82": 688, "83": 609, "84": 678, "85": 759, "86": 750, "87": 1075, "88": 739, "89": 736, "90": 599, "91": 291, "92": 417, "93": 283, "94": 432, "95": 534, "96": 288, "97": 563, "98": 577, "99": 528, "100": 592, "101": 526, "102": 390, "103": 585, "104": 636, "105": 323, "106": 293, "107": 643, "108": 319, "109": 936, "110": 638, "111": 564, "112": 605, "113": 564, "114": 433, "115": 505, "116": 359, "117": 623, "118": 609, "119": 890, "120": 644, "121": 627, "122": 541, "123": 329, "124": 209, "125": 332, "126": 646, "160": 245, "161": 248, "162": 533, "163": 575, "164": 699, "165": 684, "166": 250, "167": 607, "168": 396, "169": 765, "170": 423, "171": 465, "172": 536, "174": 765, "175": 396, "176": 364, "177": 508, "178": 402, "179": 407, "180": 288, "181": 658, "182": 550, "183": 270, "184": 244, "185": 262, "186": 446, "187": 475, "188": 690, "189": 711, "190": 798, "191": 461, "192": 738, "193": 738, "194": 738, "195": 738, "196": 738, "197": 738, "198": 1021, "199": 645, "200": 639, "201": 639, "202": 639, "203": 639, "204": 349, "205": 349, "206": 349, "207": 349, "208": 696, "209": 783, "210": 706, "211": 706, "212": 706, "213": 706, "214": 706, "215": 521, "216": 706, "217": 759, "218": 759, "219": 759, "220": 759, "221": 736, "222": 630, "223": 645, "224": 563, "225": 563, "226": 563, "227": 563, "228": 563, "229": 563, "230": 840, "231": 528, "232": 526, "233": 526, "234": 526, "235": 526, "236": 323, "237": 323, "238": 323, "239": 323, "240": 594, "241": 638, "242": 564, "243": 564, "244": 564, "245": 564, "246": 564, "247": 553, "248": 564, "249": 623, "250": 623, "251": 623, "252": 623, "253": 627, "254": 602, "255": 627, "256": 738, "257": 563, "258": 738, "259": 563, "260": 738, "261": 563, "262": 645, "263": 528, "264": 645, "265": 528, "266": 645, "267": 528, "268": 645, "269": 528, "270": 696, "271": 592, "272": 696, "273": 592, "274": 639, "275": 526, "276": 639, "277": 526, "278": 639, "279": 526, "280": 639, "281": 526, "282": 639, "283": 526, "284": 670, "285": 585, "286": 670, "287": 585, "288": 670, "289": 585, "290": 670, "291": 585, "292": 781, "293": 636, "294": 781, "295": 636, "296": 349, "297": 323, "298": 349, "299": 323, "300": 349, "301": 323, "302": 349, "303": 323, "304": 349, "305": 323, "306": 930, "307": 615, "308": 587, "309": 293, "310": 754, "311": 643, "312": 636, "313": 586, "314": 319, "315": 586, "316": 319, "317": 586, "318": 319, "319": 586, "320": 319, "321": 586, "322": 319, "323": 783, "324": 638, "325": 783, "326": 638, "327": 783, "328": 638, "329": 638, "330": 773, "331": 626, "332": 706, "333": 564, "334": 706, "335": 564, "336": 706, "337": 564, "338": 991, "339": 893, "340": 688, "341": 433, "342": 688, "343": 433, "344": 688, "345": 433, "346": 609, "347": 505, "348": 609, "349": 505, "350": 609, "351": 505, "352": 609, "353": 505, "354": 678, "355": 359, "356": 678, "357": 359, "358": 678, "359": 359, "360": 759, "361": 623, "362": 759, "363": 623, "364": 759, "365": 623, "366": 759, "367": 623, "368": 759, "369": 623, "370": 759, "371": 623, "372": 1075, "373": 890, "374": 736, "375": 627, "376": 736, "377": 599, "378": 541, "379": 599, "380": 541, "381": 599, "382": 541, "383": 368, "399": 684, "402": 389, "416": 706, "417": 587, "431": 866, "432": 679, "496": 293, "506": 738, "507": 563, "508": 1021, "509": 840, "510": 706, "511": 564, "536": 609, "537": 505, "538": 678, "539": 359, "567": 293, "601": 546, "700": 230, "710": 392, "711": 392, "713": 396, "728": 378, "729": 187, "730": 266, "731": 229, "732": 405, "733": 455, "755": 263, "768": 0, "769": 0, "770": 0, "771": 0, "772": 0, "774": 0, "775": 0, "776": 0, "777": 0, "778": 0, "779": 0, "780": 0, "783": 0, "786": 0, "795": 0, "803": 0, "805": 0, "806": 0, "807": 0, "808": 0, "821": 0, "822": 0, "823": 0, "824": 0, "900": 251, "901": 426, "902": 738, "903": 270, "904": 764, "905": 913, "906": 482, "908": 722, "910": 869, "911": 696, "912": 344, "913": 738, "914": 656, "915": 597, "916": 713, "917": 639, "918": 599, "919": 781, "920": 717, "921": 353, "922": 754, "923": 716, "924": 1009, "925": 783, "926": 608, "927": 706, "928": 782, "929": 647, "931": 575, "932": 678, "933": 736, "934": 736, "935": 702, "936": 846, "937": 657, "938": 353, "939": 736, "940": 593, "941": 541, "942": 599, "943": 344, "944": 589, "945": 593, "946": 585, "947": 543, "948": 564, "949": 541, "950": 500, "951": 599, "952": 577, "953": 344, "954": 636, "955": 548, "956": 658, "957": 570, "958": 531, "959": 561, "960": 637, "961": 574, "962": 523, "963": 606, "964": 482, "965": 589, "966": 757, "967": 582, "968": 811, "969": 827, "970": 344, "971": 589, "972": 561, "973": 589, "974": 827, "977": 675, "978": 624, "982": 880, "1024": 639, "1025": 639, "1026": 782, "1027": 597, "1028": 635, "1029": 609, "1030": 348, "1031": 349, "1032": 582, "1033": 1055, "1034": 1090, "1035": 851, "1036": 715, "1037": 784, "1038": 687, "1039": 782, "1040": 738, "1041": 668, "1042": 661, "1043": 597, "1044": 769, "1045": 639, "1046": 1052, "1047": 596, "1048": 784, "1049": 784, "1050": 715, "1051": 745, "1052": 1009, "1053": 781, "1054": 707, "1055": 782, "1056": 647, "1057": 651, "1058": 678, "1059": 687, "1060": 803, "1061": 739, "1062": 798, "1063": 758, "1064": 1032, "1065": 1041, "1066": 878, "1067": 961, "1068": 664, "1069": 619, "1070": 966, "1071": 672, "1072": 563, "1073": 572, "1074": 625, "1075": 531, "1076": 658, "1077": 526, "1078": 903, "1079": 519, "1080": 692, "1081": 692, "1082": 649, "1083": 651, "1084": 861, "1085": 691, "1086": 561, "1087": 692, "1088": 605, "1089": 528, "1090": 607, "1091": 627, "1092": 729, "1093": 644, "1094": 677, "1095": 671, "1096": 941, "1097": 948, "1098": 722, "1099": 891, "1100": 612, "1101": 526, "1102": 855, "1103": 641, "1104": 526, "1105": 526, "1106": 636, "1107": 531, "1108": 541, "1109": 505, "1110": 322, "1111": 323, "1112": 292, "1113": 880, "1114": 927, "1115": 636, "1116": 649, "1117": 692, "1118": 627, "1119": 692, "1120": 920, "1121": 835, "1122": 664, "1123": 607, "1124": 934, "1125": 803, "1126": 729, "1127": 610, "1128": 1024, "1129": 918, "1130": 943, "1131": 811, "1132": 1245, "1133": 1109, "1134": 552, "1135": 488, "1136": 846, "1137": 811, "1138": 684, "1139": 582, "1140": 688, "1141": 580, "1142": 688, "1143": 580, "1144": 1333, "1145": 1172, "1146": 708, "1147": 563, "1148": 897, "1149": 770, "1150": 920, "1151": 830, "1152": 609, "1153": 520, "1154": 537, "1155": 0, "1156": 0, "1157": 0, "1158": 0, "1160": 984, "1161": 945, "1162": 784, "1163": 692, "1164": 664, "1165": 602, "1166": 680, "1167": 605, "1168": 596, "1169": 531, "1170": 597, "1171": 531, "1172": 646, "1173": 576, "1174": 1052, "1175": 903, "1176": 596, "1177": 519, "1178": 715, "1179": 649, "1180": 753, "1181": 700, "1182": 715, "1183": 620, "1184": 935, "1185": 819, "1186": 781, "1187": 691, "1188": 1006, "1189": 778, "1190": 1059, "1191": 924, "1192": 784, "1193": 658, "1194": 651, "1195": 528, "1196": 678, "1197": 607, "1198": 736, "1199": 543, "1200": 736, "1201": 543, "1202": 739, "1203": 644, "1204": 927, "1205": 835, "1206": 758, "1207": 671, "1208": 759, "1209": 671, "1210": 767, "1211": 636, "1212": 768, "1213": 664, "1214": 768, "1215": 664, "1216": 348, "1217": 1052, "1218": 903, "1219": 713, "1220": 600, "1221": 745, "1222": 651, "1223": 773, "1224": 683, "1225": 781, "1226": 691, "1227": 758, "1228": 671, "1229": 1009, "1230": 861, "1231": 348, "1232": 738, "1233": 563, "1234": 738, "1235": 563, "1236": 1021, "1237": 840, "1238": 639, "1239": 526, "1240": 684, "1241": 546, "1242": 684, "1243": 546, "1244": 1052, "1245": 903, "1246": 596, "1247": 519, "1248": 565, "1249": 567, "1250": 784, "1251": 692, "1252": 784, "1253": 692, "1254": 707, "1255": 561, "1256": 684, "1257": 582, "1258": 684, "1259": 582, "1260": 619, "1261": 526, "1262": 687, "1263": 627, "1264": 687, "1265": 627, "1266": 687, "1267": 627, "1268": 758, "1269": 671, "1270": 597, "1271": 531, "1272": 961, "1273": 891, "1274": 597, "1275": 531, "1276": 739, "1277": 644, "1278": 739, "1279": 644, "1280": 662, "1281": 592, "1282": 850, "1283": 840, "1284": 795, "1285": 709, "1286": 592, "1287": 567, "1288": 963, "1289": 845, "1290": 1017, "1291": 904, "1292": 628, "1293": 532, "1294": 729, "1295": 664, "1296": 628, "1297": 541, "1298": 745, "1299": 651, "7680": 738, "7681": 563, "7742": 1009, "7743": 936, "7808": 1075, "7809": 890, "7810": 1075, "7811": 890, "7812": 1075, "7813": 890, "7838": 720, "7840": 738, "7841": 563, "7842": 738, "7843": 563, "7844": 738, "7845": 563, "7846": 738, "7847": 563, "7848": 738, "7849": 563, "7850": 738, "7851": 563, "7852": 738, "7853": 563, "7854": 738, "7855": 563, "7856": 738, "7857": 563, "7858": 738, "7859": 563, "7860": 738, "7861": 563, "7862": 738, "7863": 563, "7864": 639, "7865": 526, "7866": 639, "7867": 526, "7868": 639, "7869": 526, "7870": 639, "7871": 526, "7872": 639, "7873": 526, "7874": 639, "7875": 526, "7876": 639, "7877": 526, "7878": 639, "7879": 526, "7880": 349, "7881": 323, "7882": 349, "7883": 323, "7884": 706, "7885": 564, "7886": 706, "7887": 564, "7888": 706, "7889": 564, "7890": 706, "7891": 564, "7892": 706, "7893": 564, "7894": 706, "7895": 564, "7896": 706, "7897": 564, "7898": 706, "7899": 587, "7900": 706, "7901": 587, "7902": 706, "7903": 587, "7904": 706, "7905": 587, "7906": 706, "7907": 587, "7908": 759, "7909": 623, "7910": 759, "7911": 623, "7912": 866, "7913": 679, "7914": 866, "7915": 679, "7916": 866, "7917": 679, "7918": 866, "7919": 679, "7920": 866, "7921": 679, "7922": 736, "7923": 627, "7924": 736, "7925": 627, "7926": 736, "7927": 627, "7928": 736, "7929": 627, "8013": 706, "8189": 251, "8190": 191, "8192": 195, "8193": 391, "8194": 195, "8195": 391, "8196": 130, "8197": 98, "8198": 65, "8199": 215, "8200": 105, "8201": 78, "8202": 39, "8203": 0, "8211": 670, "8212": 779, "8213": 779, "8215": 542, "8216": 234, "8217": 230, "8218": 246, "8219": 255, "8220": 407, "8221": 410, "8222": 415, "8224": 536, "8225": 555, "8226": 343, "8229": 490, "8230": 713, "8240": 938, "8242": 219, "8243": 382, "8249": 278, "8250": 281, "8252": 495, "8260": 431, "8308": 447, "8319": 462, "8355": 618, "8356": 586, "8359": 869, "8363": 606, "8364": 519, "8453": 697, "8467": 483, "8470": 1181, "8482": 605, "8486": 657, "8494": 568, "8539": 799, "8540": 867, "8541": 896, "8542": 815, "8706": 592, "8710": 713, "8719": 738, "8721": 618, "8722": 546, "8725": 431, "8730": 563, "8734": 1032, "8747": 347, "8776": 585, "8800": 538, "8804": 499, "8805": 505, "9674": 519, "63171": 0, "64257": 653, "64258": 694, "64259": 1025, "64260": 1084, "65279": 0, "65532": 1071, "65533": 946}, "CIDtoGID_Compressed": true, "CIDtoGID": "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", "_version_": 6}
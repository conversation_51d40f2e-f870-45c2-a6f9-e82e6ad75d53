<?php

use App\Http\Controllers\BlogController;
use App\Http\Controllers\MainController;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\AboutController;
use App\Http\Controllers\CareerController;
use App\Http\Controllers\ContactsController;
use App\Http\Controllers\Loop\NewsController as LoopNewsController;
use App\Http\Controllers\Loop\EventController as LoopEventController;
use App\Http\Controllers\MediaContactController;
use App\Http\Controllers\NewsController;
use App\Http\Controllers\PressController;
use App\Http\Controllers\PeopleController;
use App\Http\Controllers\PortfolioController;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

// Home route with new HomeController
Route::multilingual('/', [HomeController::class, 'index'])->name('home');

// Ostatní routy - using route keys instead of hardcoded paths
Route::multilingual('about', [AboutController::class, 'index'])->name('about');
Route::multilingual('people', [PeopleController::class, 'index'])->name('people');
Route::multilingual('portfolio', [PortfolioController::class, 'index'])->name('portfolio');
//Route::multilingual('pressroom', [NewsController::class, 'index'])->name('pressroom');
Route::multilingual('career', [CareerController::class, 'index'])->name('career');
Route::multilingual('contacts', [ContactsController::class, 'index'])->name('contacts');

// Code of conduct and anti-bribery policy
Route::multilingual('code-of-conduct', [MainController::class, '__invoke'])->name('code-of-conduct');
Route::multilingual('anti-bribery-and-anti-corruption-policy', [MainController::class, '__invoke'])->name('anti-bribery-and-anti-corruption-policy');


Route::multilingual('news', [NewsController::class, 'index'])->name('news');
Route::multilingual('news/press', [PressController::class, 'index'])->name('news.press');
Route::multilingual('media-contact', [MediaContactController::class, 'index'])->name('media-contact');

Route::multilingual('news/blog', [BlogController::class, 'index'])->name('news.blog');
Route::multilingual('news/blog/{slug}', [BlogController::class, 'show'])->name('news.blog.show');

Route::get('/news/{slug}', [NewsController::class, 'show'])->name('cs.news.show');
Route::get('/en/news/{slug}', [NewsController::class, 'show'])->name('en.news.show');


// Loop sekce
Route::middleware(['auth:sanctum', 'verified'])->get('/loop', function () {
    return Inertia::render('Dashboard/Index');
})->name('dashboard');

Route::group(['middleware' => 'auth:sanctum'], function () {
    Route::group(['prefix' => 'loop', 'as' => 'loop.'], function () {
        Route::resource('news', LoopNewsController::class);
        Route::resource('event', LoopEventController::class);
    });
});



// Original

//Route::multilingual('/', 'HomeController')->name('home');
//Route::multilingual('/team', 'TeamController');
//
//Route::multilingual('/portfolio', 'PortfolioController');
//Route::multilingual('/portfolio/private', 'PortfolioPrivateController');
//Route::multilingual('/portfolio/private/reinsberg-group', 'PortfolioPrivateController');
//Route::multilingual('/portfolio/private/koettermann-group', 'PortfolioPrivateController');
//Route::multilingual('/portfolio/venture', 'PortfolioPrivateController');
//Route::multilingual('/portfolio/real-estate', 'PortfolioPrivateController');
//Route::multilingual('/portfolio/real-estate/residential', 'PortfolioPrivateController');
//Route::multilingual('/portfolio/real-estate/commercial', 'PortfolioPrivateController');
//Route::multilingual('/portfolio/renewables', 'PortfolioPrivateController');
//
//Route::multilingual('/career', 'CareerController');
//Route::multilingual('/contact', 'ContactController');
//
//// CODEX
//Route::multilingual('/code-of-conduct', 'CareerController');
//Route::multilingual('/anti-bribery-and-anti-corruption-policy', 'CareerController');
//
//Route::multilingual('/news', 'NewsListController');
//Route::multilingual('/news/press', 'NewsListController');
//
//Route::multilingual('/news/blog', 'BlogController');
//Route::multilingual('/news/blog/{slug}', 'BlogController');
//
//Route::get('/news/{slug}', 'NewsController@show')->name('cs.news.show');
//Route::get('/en/news/{slug}', 'NewsController@show')->name('en.news.show');

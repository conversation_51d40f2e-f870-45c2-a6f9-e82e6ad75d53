<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use League\Csv\Reader;
use League\Csv\Statement;


class RestaurantDrinkItemSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {


//        $pivoCategoryId = DB::table('restaurant_drinks')->where('name', 'PIVO')->value('id');

        // CSV data stored in a variable
        $csvData = <<<'CSV'
name;volume;price;category_name
0,7 VÝBĚR Z VINOTÉKY U OBSLUHY;;;VÍNO
"TOČENÁ LIMONÁDA
malina, bezinka, hrozno, cola, multivitamín, ledový čaj, broskev";0,3;39 Kč;NEALKOHOLICKÉ NÁPOJE
LEDOVÁ KÁVA SE ZMRZLINOU       ;;89 Kč;TEPLÉ NÁPOJE
JOHNNIE WALKER;0,04;70 Kč;DESTILÁTY A LIKÉRY
GROG             ;;70 Kč;TEPLÉ NÁPOJE
BALLANTINES;0,04;75 Kč;DESTILÁTY A LIKÉRY
JACK DANIELS;0,04;85 Kč;DESTILÁTY A LIKÉRY
"TOČENÁ LIMONÁDA
malina, bezinka, hrozno, cola, multivitamín, ledový čaj, broskev";0,5;55 Kč;NEALKOHOLICKÉ NÁPOJE
VODKA FINSKÁ;0,04;65 Kč;DESTILÁTY A LIKÉRY
SKLENICE VODY Z KOHOUTKU;0,3;20 Kč;NEALKOHOLICKÉ NÁPOJE
IRSKÁ KÁVA                        ;;89 Kč;TEPLÉ NÁPOJE
TURECKÁ KÁVA           	          ;;49 Kč;TEPLÉ NÁPOJE
KRUŠOVICKÉ HOŘKÉ NEALKO;0,3;38 Kč;NEALKOHOLICKÉ PIVO
VÍDEŇSKÁ KÁVA                    ;;65 Kč;TEPLÉ NÁPOJE
TOČENÉ ŘEZANÉ PIVO                                                                  ;0,3;49 Kč;PIVO
KRUŠOVICE ČERNÉ VÝČEPNÍ;0,5;59 Kč;PIVO
KRUŠOVICKÉ HOŘKÉ NEALKO;0,5;49 Kč;NEALKOHOLICKÉ PIVO
CAFFE LATE                      ;;89 Kč;TEPLÉ NÁPOJE
ALŽÍRSKÁ KÁVA                    ;;79 Kč;TEPLÉ NÁPOJE
SLIVOVICE;0,04;65 Kč;DESTILÁTY A LIKÉRY
KRUŠOVICE ČERNÉ VÝČEPNÍ;0,3;49 Kč;PIVO
CAPUCCINO                         ;;59 Kč;TEPLÉ NÁPOJE
CINZANO BITTER          							      	;0,1;70 Kč;APERITIVY
CINZANO ROSSO								      	;0,1;70 Kč;APERITIVY
PUNČ Z GRIOTKY                  ;;70 Kč;TEPLÉ NÁPOJE
CINZANO BIANCO     							      	;0,1;70 Kč;APERITIVY
ČAJ                                 ;;55 Kč;TEPLÉ NÁPOJE
ESPRESSO S MLÉKEM             ;;49 Kč;TEPLÉ NÁPOJE
MARTINI BIANCO								      ;0,1;70 Kč;APERITIVY
MARTINI ROSSO 								      	;0,1;70 Kč;APERITIVY
BAILEYS;0,04;75 Kč;DESTILÁTY A LIKÉRY
BOHEMIA SEKT BRUT /suché/				               ;0,75;270 Kč;SEKTY
"ZLATOPRAMEN RADLER GREP n.a.
plechovka";0,4;45 Kč;NEALKOHOLICKÉ PIVO
KRUŠOVICE MUŠKETÝR                                      ;0,5;55 Kč;PIVO
BOHEMIA DEMI SEC					                         ;0,75;270 Kč;SEKTY
KRUŠOVICE MUŠKETÝR                                                          ;0,3;45 Kč;PIVO
ROZLÉVANÉ VÍNO					                 ;0,2;80 Kč;VÍNO
FERNET;0,04;59 Kč;DESTILÁTY A LIKÉRY
JÄGERMEISTER;0,04;65 Kč;DESTILÁTY A LIKÉRY
PEPRMINT;0,04;58 Kč;DESTILÁTY A LIKÉRY
TOČENÉ ŘEZANÉ PIVO                                                                  ;0,5;59 Kč;PIVO
RUM BOŽKOV;0,04;58 Kč;DESTILÁTY A LIKÉRY
GRIOTKA;0,04;58 Kč;DESTILÁTY A LIKÉRY
BECHEROVKA;0,04;65 Kč;DESTILÁTY A LIKÉRY
VODKA JELZIN;0,04;65 Kč;DESTILÁTY A LIKÉRY
JAMESON;0,04;60 Kč;DESTILÁTY A LIKÉRY
MATTONI - neperlivá, jemně perlivá, perlivá;0,3;39 Kč;NEALKOHOLICKÉ NÁPOJE
TONIC, TONIC ZÁZVOROVÝ;0,25;42 Kč;NEALKOHOLICKÉ NÁPOJE
PEPSI;0,25;42 Kč;NEALKOHOLICKÉ NÁPOJE
MIRINDA;0,25;42 Kč;NEALKOHOLICKÉ NÁPOJE
METAXA ***;0,04;70 Kč;DESTILÁTY A LIKÉRY
METAXA *****;0,04;90 Kč;DESTILÁTY A LIKÉRY
7UP;0,25;42 Kč;NEALKOHOLICKÉ NÁPOJE
CINZANO EXTRA DRY							      	;0,1;70 Kč;APERITIVY
JIM BEAM;0,04;70 Kč;DESTILÁTY A LIKÉRY
TULLAMORE DEW;0,04;70 Kč;DESTILÁTY A LIKÉRY
CSV;

        // Parse the CSV data
        $csv = Reader::createFromString($csvData);
        $csv->setDelimiter(';');
        $csv->setHeaderOffset(0);

        // Retrieve the records as an array
        $records = Statement::create()->process($csv);

        // Insert records into the database
        foreach ($records as $record) {
            DB::table('restaurant_drink_items')->insert([
                'restaurant_drink_id' => DB::table('restaurant_drinks')->where('name', $record['category_name'])->value('id'),
                'name' => $record['name'],
                'volume' => $record['volume'],
                'price' => $record['price'],
            ]);
        }

    }
}

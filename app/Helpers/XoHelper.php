<?php

namespace App\Helpers;

class XoHelper
{
    public static function bytesToHuman($bytes)
    {
        $units = ['B', 'KiB', 'MiB', 'GiB', 'TiB', 'PiB'];

        for ($i = 0; $bytes > 1024; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, 2) . ' ' . $units[$i];
    }


    public static function dateWithTag($date)
    {

        $d = \Carbon\Carbon::parse($date)->format('j. n. Y');


        return '<time datetime="' . $date . '">' . $d . '</time>';
    }

    // https://github.com/spatie/laravel-navigation

    public static function printNav($items, $request)
    {
        echo '<ul class="uk-navbar-nav">' . XoHelper::_nav($items, $request) . '</ul>';


    }

    public static function _nav($items, $request)
    {
        $t = '';
        foreach ($items as $item) {

            $class = "";

            if (isset($item['routeActive'])) {
                $class = request()->routeIs($item['routeActive']) ? 'uk-active' : '';
            }
            if (isset($item['urlActive'])) {
                $class = request()->is($item['urlActive']) ? 'uk-active' : '';
            }


            if (isset($item['childern'])) {

                $t .= '
                <li class="uk-parent ' . $class . '"><a href="' . $item['url'] . '">' . $item['title'] . '</a>
                    <div class="uk-navbar-dropdown">
                        <div class="uk-navbar-dropdown-grid uk-child-width-1-1" uk-grid>
                            <div>
                                <ul class="uk-nav uk-navbar-dropdown-nav">
                                ' . XoHelper::_nav($item['childern'], $request) . '
                                </ul>
                            </div>
                        </div>
                    </div>
                </li>';

            } else {
                $t .= '<li class="' . $class . '"><a href="' . $item['url'] . '">' . $item['title'] . '</a></li>';
            }
        }

        return $t;

    }

    public static function navigationItem($title, $route, $routeActive = null, $routeParams = null): string
    {

        if (request()->routeIs($routeActive)) {
            $class = 'uk-active';
        } else if (request()->routeIs($route)) {
            $class = 'uk-active';
        } else {
            $class = '';
        }

        if ($route) {
            $href = route($route, $routeParams);
        } else {
            $href = "#";
        }

        return '<li class="' . $class . '"><a href="' . $href . '">' . $title . '</a></li>';
    }
    public static function parentClass($routeActive): string
    {

        $class = request()->routeIs($routeActive) ? 'uk-active' : '';

        return ' class="'.$class.'"';
    }


    public static function printMobNav($items, $request)
    {
        echo '<ul class="uk-nav uk-nav-primary uk-nav">' . XoHelper::_navMob($items, $request) . '</ul>';


    }

    public static function _navMob($items, $request)
    {
        $t = '';
        foreach ($items as $item) {

            $class = "";

            if (isset($item['routeActive'])) {
                $class = request()->routeIs($item['routeActive']) ? 'uk-active' : '';
            }
            if (isset($item['urlActive'])) {
                // $class = request()->routeIs($item['routeActive']) ? 'uk-active' : '';
            }

            if (isset($item['childern'])) {

                $t .= '
                <li class="uk-parent ' . $class . '"><a href="' . $item['url'] . '">' . $item['title'] . '</a>
                    <ul class="uk-nav-sub">
                    ' . XoHelper::_navMob($item['childern'], $request) . '
                    </ul>
                </li>';

            } else {
                $t .= '<li class="' . $class . '"><a href="' . $item['url'] . '">' . $item['title'] . '</a></li>';
            }
        }

        return $t;

    }

    public static function priceWithVat($price, $vat = 1.21)
    {
        $priceVat = $price * $vat;
        return number_format($priceVat, 0, ',', strlen((int)$priceVat) > 4 ? ' ' : '');
    }

}



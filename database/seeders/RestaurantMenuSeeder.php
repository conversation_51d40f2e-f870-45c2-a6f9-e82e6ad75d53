<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class RestaurantMenuSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $menuItems = [
            ['name' => 'PŘEDKRMY', 'order' => 1, 'created_by' => 1, 'edited_by' => 1, 'break' => false, 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'POLÉVKY', 'order' => 2, 'created_by' => 1, 'edited_by' => 1, 'break' => false, 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'HLAVNÍ POKRMY', 'order' => 4, 'created_by' => 1, 'edited_by' => 1, 'break' => true, 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'ČERSTVÉ SALÁTY', 'order' => 7, 'created_by' => 1, 'edited_by' => 1, 'break' => false, 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'DEZERTY', 'order' => 8, 'created_by' => 1, 'edited_by' => 1, 'break' => false, 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'PŘÍLOHY K JÍDLŮM', 'order' => 9, 'created_by' => 1, 'edited_by' => 1, 'break' => false, 'created_at' => now(), 'updated_at' => now()],
        ];

        DB::table('restaurant_menus')->insert($menuItems);
    }
}

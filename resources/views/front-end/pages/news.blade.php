@extends('front-end.layouts.app')

@section('title', __('navigation.pressroom'))

@section('content')

    <div class="news-background"
         uk-scrollspy="target: [uk-scrollspy-class]; cls: uk-animation-fade; delay: false;">
        @include('front-end.components.navbar', ['navMode' => 'black'])

        <div class="uk-section uk-section-large ">
            <div class="uk-container uk-container-large">
                <h2 class="uk-h1 uk-heading-small uk-text-uppercase uk-text-center@s">Pressroom</h2>

                @include('front-end.components.news-navigation')
                <div class="uk-grid" uk-grid>
                    <div class="uk-width-1-1">
                        <div class="uk-child-width-1-4@m uk-child-width-1-2@s uk-margin-xlarge-bottom" uk-grid
                             uk-height-match="target: > div > .uk-card">
                            @foreach ($items as $item)
                                <div>
                                    @php
                                        $slugField = 'slug_' . app()->getLocale();
                                        $slug = isset($item[$slugField]) && !empty($item[$slugField])
                                            ? $item[$slugField]
                                            : ($item['slug_en'] ?? $item['slug_cs']);
                                    @endphp
                                    <a class="news-card uk-link-toggle uk-display-block uk-scrollspy-inview"
                                       href="{{ $item['type'] === 'blog' ? route(locale().'.news.blog.show', $slug) : ($item['has_detail'] ? route(locale().'.news.show', $slug) : '#') }}"
                                       aria-label="Read more" uk-scrollspy-class="">
                                        <div class="news-card-media">
                                            <img
                                                src="{{ $item['type'] === 'blog' ? '/images/blog/th/'.($item['img'] ?? $slug.'.jpg') : '/images/'.$item['type'].'/th/'.$slug.'.jpg' }}"
                                                alt="" loading="lazy">
                                        </div>
                                        <div class="news-card-content">
                                            <div class="news-card-meta">
                                                <div class="news-card-date">
                                                    @php
                                                        $date = \Carbon\Carbon::parse($item['date']);
                                                        if(app()->getLocale() == 'cs') {
                                                            $dateFormatted = $date->locale('cs')->isoFormat('D. MMMM YYYY');
                                                        } else {
                                                            $dateFormatted = $date->locale('en')->isoFormat('MMMM D, YYYY');
                                                        }
                                                    @endphp
                                                    {{ $dateFormatted }}
                                                </div>
                                                <span
                                                    class="news-card-type type-{{ $item['type'] === 'blog' ? 'blog' : 'news' }}">
                                                {{ $item['type'] === 'blog' ? __('news.blog') : __('news.tz') }}
                                            </span>
                                            </div>
                                            <h3 class="news-card-title">
                                                {{ $item['title_'.app()->getLocale()] ?: $item['title_en'] }}
                                            </h3>
                                        </div>
                                    </a>
                                </div>
                            @endforeach

                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>

    @include('front-end.components.footer')

@endsection

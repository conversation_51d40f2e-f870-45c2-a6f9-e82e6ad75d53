<script setup>

import loopMorphingBg from '@/Loop/MorphingBg';

</script>


<template>

    <loopMorphingBg/>

    <div class="uk-position-center uk-overlay uk-overlay-default loop-glass-box uk-light">

        <div>
            <slot name="logo" />
        </div>

        <div class="w-full sm:max-w-md mt-6 px-6 py-4 bg-white shadow-md overflow-hidden sm:rounded-lg">
            <slot />
        </div>

    </div>


</template>


<style>
.loop-glass-box {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}
</style>

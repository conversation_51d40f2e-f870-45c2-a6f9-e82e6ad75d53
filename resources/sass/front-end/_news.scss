// News page styles
.news-background {
    background-image: url('/images/home/<USER>');
    background-size: 4000px;
    background-position: top center;

    @media (max-width: 767px) {
        background-size: 600%;
    }
}

.news-detail-page {
    .uk-section-muted {
        background-image: url('/images/home/<USER>');
        background-size: 6000px;
        background-position: center top;

    }

    .uk-section {
        background-color: #f1f1f1;
    }
}

// News navigation container
.news-nav-container {
    @media (max-width: 767px) {
        margin-bottom: 20px !important;
    }
}

// News navigation flex container
.news-nav-flex {
    @media (max-width: 767px) {
        justify-content: flex-start !important;
    }
}

// Media Contact styles
.media-contact-link {
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
    text-decoration: none;
    padding: 8px 15px;
    border-radius: 20px;
    background-color: rgba(189, 159, 87, 0.1);
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-60%);

    &:hover {
        background-color: rgba(189, 159, 87, 0.2);
        text-decoration: none;
        transform: translateY(-55%);
    }

    &.uk-active {
        background-color: #bd9f57;
        color: #fff !important;
        box-shadow: 0 3px 10px rgba(189, 159, 87, 0.3);
    }

    @media (max-width: 767px) {
        position: static;
        display: inline-flex;
        align-items: center;
        text-align: left;
        margin: 0 0 0 15px;
        transform: none;
        padding: 5px 10px;
        font-size: 13px;

        &:hover {
            transform: translateY(-2px);
        }

        .media-contact-text {
            @media (max-width: 479px) {
                display: none;
            }
        }
    }
}

.media-contact-card {
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    background-color: #fff;

    .media-contact-photo-container {
        width: 180px;
        height: 180px;
        margin: 0 auto;
        border-radius: 50%;
        overflow: hidden;
        border: 5px solid rgba(189, 159, 87, 0.2);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .media-contact-photo {
        width: 100%;
        height: 100%;
        object-fit: cover;
        object-position: center top;
    }
}

// Main tabs styles
.uk-tab {
    border-bottom-color: rgba(189, 159, 87, 0.2);
    text-align: center;

    @media (max-width: 767px) {
        text-align: left;
    }

    > * {
        float: none;
        display: inline-block;

        > a {
            font-size: 18px;
            font-weight: 500;
            text-transform: uppercase;
            color: #666;
            border-bottom: 2px solid transparent;
            transition: all 0.2s ease;

            &:hover {
                color: #bd9f57;
                border-bottom-color: rgba(189, 159, 87, 0.3);
            }

            @media (max-width: 767px) {
                font-size: 14px;
                padding: 8px 10px;
            }
        }
    }

    > .uk-active > a {
        color: #bd9f57;
        border-bottom-color: #bd9f57;
    }

    //&:before {
    //    left: 30%;
    //    right: 30%;
    //
    //    @media (max-width: 767px) {
    //        left: 0;
    //        right: 70%;
    //    }
    //}
}

// Filter styles
.news-filter {
    text-align: center;
    margin-top: 30px;
    margin-bottom: 40px;

    @media (max-width: 767px) {
        margin-top: 15px;
        margin-bottom: 20px;
        text-align: left;
    }

    .uk-subnav-pill {
        background-color: #f8f8f8;
        border-radius: 30px;
        padding: 8px 10px;
        display: inline-flex;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        margin-left: auto;
        margin-right: auto;
        justify-content: center;
        flex-wrap: wrap;

        @media (max-width: 767px) {
            padding: 5px 8px;
            margin-left: 0;
            justify-content: flex-start;
        }

        > * {
            margin: 0;
            padding: 0 5px;

            &:first-child {
                padding-left: 0px;
            }

            &:last-child {
                padding-right: 5px;
            }

            > a {
                border-radius: 20px;
                padding: 10px 25px;
                font-weight: 500;
                font-size: 14px;
                transition: all 0.2s ease;
                color: #666;
                text-align: center;

                &:hover {
                    background-color: rgba(189, 159, 87, 0.1);
                    color: #bd9f57;
                }

                @media (max-width: 767px) {
                    padding: 6px 12px;
                    font-size: 13px;
                }
            }

            &.uk-active > a {
                background-color: #bd9f57;
                color: #fff;
                box-shadow: 0 3px 10px rgba(189, 159, 87, 0.3);
            }
        }
    }
}

// Card styles
.news-card {
    height: 100%;
    display: flex;
    flex-direction: column;
    transition: all 0.2s ease;
    background: #ffffff;
    color: #333;
    border: 1px solid #e5e5e5;
    border-radius: 10px;
    overflow: hidden;
    position: relative;

    &:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
    }
}

.news-card-media {
    position: relative;
    overflow: hidden;
}

.news-card-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;

    margin-top: -28px;
    position: relative;
    z-index: 1;
    margin-bottom: 0px;
}

.news-card-date {
    display: inline-block;
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
    color: #666;
    background-color: #f5f5f5;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

}

.news-card-type {
    display: inline-block;
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
    color: #fff;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

    &.type-blog {
        background-color: #bd9f57; // Success color for blog
    }

    &.type-news {
        background-color: #0b1215; // Primary color for news
    }
}

.news-card-content {
    padding: 15px 20px;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.news-card-title {
    font-size: 16px;
    margin-bottom: 0;
    margin-top: 25px;
    color: #333;
    font-weight: 500;
}

// Responsive adjustments
@media (max-width: 959px) {
    .news-card-meta {
        //flex-direction: column;
        //align-items: flex-start;
    }

    .news-card-date {
        //margin-bottom: 5px;
    }

    .news-card-type {
        //align-self: flex-end;
    }

    // Press page mobile adjustments
    .uk-tab > * > a {
        font-size: 16px;
        padding: 8px 12px;
    }

    .uk-heading-medium {
        font-size: 2rem;
    }

    // Media coverage reference items
    article.uk-panel {
        margin-bottom: 15px;
    }

    article.uk-panel .uk-text-meta {
        font-size: 12px;
    }

    article.uk-panel h3.uk-h5 {
        font-size: 14px;
        line-height: 1.4;
        margin-top: 5px;
    }
}

// Press page specific styles
.press-reference-item {
    display: flex;
    align-items: center;
    margin-bottom: 15px;

    @media (max-width: 767px) {
        flex-direction: column;
        align-items: flex-start;

        .reference-logo {
            margin-bottom: 10px;
            margin-right: 0;
        }
    }

    .reference-logo {
        margin-right: 15px;
        flex-shrink: 0;

        img {
            max-width: 120px;
            max-height: 30px;
            width: auto;
            height: auto;
            filter: grayscale(100%);
            opacity: 0.8;
            transition: all 0.3s ease;

            &:hover {
                filter: grayscale(0);
                opacity: 1;
            }
        }
    }

    .reference-content {
        flex-grow: 1;

        .reference-date {
            font-size: 12px;
            color: #666;
            margin-bottom: 5px;
        }

        .reference-title {
            font-size: 16px;
            font-weight: 500;
            margin: 0;
            line-height: 1.4;

            a {
                color: #333;
                text-decoration: none;
                transition: color 0.2s ease;

                &:hover {
                    color: #bd9f57;
                }
            }
        }
    }
}

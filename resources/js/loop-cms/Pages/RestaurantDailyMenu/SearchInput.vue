<script setup>
import { ref, watch } from 'vue';
import debounce from 'lodash/debounce';
import {router} from "@inertiajs/vue3";


const props = defineProps({
    category: String,
});

const emit = defineEmits(['select']);

const query = ref('');
const results = ref([]);

const onInput = debounce(() => {
    searchItems(query.value);
}, 200);


import { useForm } from '@inertiajs/vue3'

async function searchItems(query) {
    try {
        const response = await axios.get(route('loop.restaurant-daily-menus.search', { q: query, category: props.category }));
        results.value = response.data;
    } catch (error) {
        console.error('Error searching items:', error);
    }
}

function selectResult(result) {
    emit('select', result);
    query.value = '';
    results.value = '';
}
</script>

<template>
    <div>
        <input
            v-model="query"
            @input="onInput"
            class="uk-input uk-flex-wrap-stretch"
            type="search"
            autocomplete="off"
            placeholder="Vyhledat dle čísla, nebo názvu..."
        >
        <ul v-if="results.length">
            <li
                v-for="(result, index) in results"
                :key="index"
                @click="selectResult(result)"
            >
                <div class="uk-grid">
                    <div class="uk-width-small">{{ result.number }}</div>
                    <div class="uk-width-expand">{{ result.name }}</div>
                    <div>{{ result.price }}</div>
                </div>

            </li>
        </ul>
    </div>
</template>

<style scoped>
ul {
    list-style-type: none;
    padding: 0;
}

li {
    cursor: pointer;
}

li:hover {
    background-color: #ffffff;
}
</style>

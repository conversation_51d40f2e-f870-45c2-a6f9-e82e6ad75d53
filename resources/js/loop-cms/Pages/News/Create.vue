<script setup>
import AppLayout from '@/Layouts/AppLayout.vue';
import UIkit from 'uikit';
import ImageUploader from '@/Helpers/ImageUploader';
import FileUploader from '@/Helpers/FileUploader';
import {useForm} from "@inertiajs/vue3";
import ActionMessage from '@/Components/ActionMessage';
import InputError from '@/Components/InputError';
import Tiptap from '@/Components/Tiptap';

const textTitle = 'Přidat novinku';

const routeStore = 'loop.news.store';

const form = useForm({
    title: null,
    content: null,
    images: [],
    media_files: [],
});


function store() {
    form.post(route(routeStore), {
        onSuccess: (response) => {
            UIkit.notification({message: 'Uloženo', status: 'success'});
        }
    });
}
</script>
<template>
    <app-layout :title="textTitle">
        <template #header>
            <div
                class="uk-child-width-expand@s uk-margin-small-top uk-flex-middle"
                uk-grid
            >
                <div class="uk-width-expand">
                    <div
                        class="uk-child-width-expand@s uk-flex uk-flex-middle"
                        uk-grid
                    >
                        <div class="uk-width-expand">
                            <div v-if="form.id">
                                Naposledy uloženo {{ formatDate(news.updated_at) }} uživatelem {{ news.editor.name }}
                            </div>
                        </div>
                    </div>
                </div>
                <div class="uk-width-auto">
                    <button
                        class="uk-button uk-button-default uk-margin-right"
                        :loading="false"
                        type="submit"
                        @click.prevent="store()"
                    >
                        Uložit
                    </button>
                </div>
            </div>
        </template>
        <div
            v-if="form.hasErrors"
            class=""
            style="color: red"
        >
            Opravte následující chyby
        </div>
        <form
            class="uk-form-stacked"
            @submit.prevent="store"
        >
            <div class="uk-margin">
                <label
                    class="uk-form-label"
                    for="form-stacked-text"
                >Titulek</label>
                <div class="uk-form-controls">
                    <input
                        id="form-stacked-text"
                        v-model="form.title"
                        class="uk-input uk-form-large"
                        name="title"
                        placeholder="Zadejte titulek stránky..."
                        type="text"
                    >
                    <InputError :message="form.errors.title"/>
                </div>
            </div>
            <div class="uk-margin">
                <label
                    class="uk-form-label"
                    for="content"
                >Text novinky</label>
                <div class="uk-form-controls">
                    <tiptap v-model="form.content" />
                    <InputError :message="form.errors.content"/>
                </div>
            </div>
            <ImageUploader
                v-model="form.images"
            />
            <FileUploader
                v-model="form.media_files"
            />
            <div class="">
                <ActionMessage
                    :on="form.recentlySuccessful"
                >
                    Uspěšně uloženo
                </ActionMessage>
                <button
                    class="uk-button uk-button-primary uk-button-large"
                    :loading="false"
                    type="submit"
                >
                    Uložit
                </button>
            </div>
        </form>
    </app-layout>
</template>

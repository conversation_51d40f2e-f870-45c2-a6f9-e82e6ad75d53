<?php

namespace Database\Seeders;

use App\Models\RestaurantMenu;
use App\Models\RestaurantMenuItem;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use League\Csv\Reader;
use League\Csv\Statement;

class RestaurantMenuItemSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {

        $csvData = <<<'CSV'
name;number;order;weight;price;text;category_name
NAŠE TIRAMISU se šlehačkou;433;0;;;;DEZERTY
"ZMRZLINOVÝ POHÁR "" CARAMELO""  ";355;1;;; vanil<PERSON><PERSON> z<PERSON>, č<PERSON><PERSON><PERSON>, karamel, šlehačka;DEZERTY
SMAŽENÝ sýr EIDAM;114;2;;;;HLAVNÍ POKRMY
Kuřecí steak „KOMBINÉ“ ;77;3;;;zapečený třemi druhy sýrů, smetanový sos;HLAVNÍ POKRMY
PALAČINKY OD NATÁLKY;332;2;;;palačinka plněná horkými malinami a vanilkovou zmrzlinou se šlehačkou a karamelem;DEZERTY
LAHŮDKOVÝ KRÁL SÝRŮ marinovaný;3;1;1 ks;;dle vlastní receptury;PŘEDKRMY
ČESKÝ  ŘÍZEK Z VEPŘOVÉ KRKOVIČKY, citrón;979;5;;;;HLAVNÍ POKRMY
GRILOVANÁ VAPŘOVÁ ŽEBÍRKA V MEDOVÉ MARINÁDĚ ;6;6;;;s čerstvými bylinkami s hořčicí a křenem, pečivo;HLAVNÍ POKRMY
Kuřecí steak „BÍLÁ PANÍ“;79;8;;;zapečený s broskví a sýrem, smetanový sos;HLAVNÍ POKRMY
TĚSTOVINY S KUŘECÍMI PRSÍČKY A ŠPENÁTOVÝM LISTEM, ;136;9;;;smetana. česnek, parmazán;HLAVNÍ POKRMY
 KUŘECÍ PRSÍČKA s grilovanou zeleninou na másle;611;12;;;;HLAVNÍ POKRMY
STEAK Z VEPŘOVÉ KRKOVICE S FAZOLOVÝMI LUSKY SE SLANINOU;148;13;;;;HLAVNÍ POKRMY
SALÁT CAESAR;307;1;;;s grilovaným kuřecím prsíčkem a slaninou, ledový salát, římský salát, dresink caesar;ČERSTVÉ SALÁTY
ŘECKÝ SALÁT S OLIVAMI A BALKÁNSKÝM SÝREM;188;2;;;rajče, okurka, paprika, olivy, balkánský sýr, oliv.olej, vinný ocet, oreagano;ČERSTVÉ SALÁTY
DLE DENNÍ NABÍDKY;1701;;;;;POLÉVKY
ARABSKÉ MASO;291;0;;;grilovaná kuřecí prsíčka s čerstvou zeleninou a česnekovým dresinkem;HLAVNÍ POKRMY
GRILOVANÁ VEPŘOVÁ PANENKA NA BAREVNÉM PEPŘI;3899;7;;;v jemné pepřové omáčce;HLAVNÍ POKRMY
Kečup;389;10;;;;PŘÍLOHY K JÍDLŮM
Pečivo;386;15;;;;PŘÍLOHY K JÍDLŮM
PIKANTNÍ Tradiční „KRUŠOVICKÁ ŘEZANKA“ ;970;4;;;masová směs z vepřové panenky a kuřecích prsíček, cibulka, paprika, slanina, žempiony na pivě;HLAVNÍ POKRMY
BIFTEK Z PRAVÉ HOVĚZÍ SVÍČKOVÉ DLE RUDOLFA II.;477;10;;;"připravený na pivovarské směsi ze slaniny, žampionů, cibule a černého piva
";HLAVNÍ POKRMY
MEDAILONKY Z VEPŘOVÉ PANENKY,;318;11;;;v hříbkové omáčce na smetaně;HLAVNÍ POKRMY
ŠOPSKÝ SALÁT;315;0;;;;ČERSTVÉ SALÁTY
Selský šťouchaný brambor – opečená cibulka, anglická slanina, petrželka;365;0;;;;PŘÍLOHY K JÍDLŮM
Opékaný brambor;371;2;;;;PŘÍLOHY K JÍDLŮM
Bramborák;384;5;;;;PŘÍLOHY K JÍDLŮM
Tatarská omáčka;387;8;;;;PŘÍLOHY K JÍDLŮM
Hořčice;388;9;;;;PŘÍLOHY K JÍDLŮM
ŘÍZEK z kuřecích prsíček, citron;984;1;;;;HLAVNÍ POKRMY
Bramborové hranolky;374;1;;;;PŘÍLOHY K JÍDLŮM
Vařený brambor;373;3;;;;PŘÍLOHY K JÍDLŮM
Krokety;368;4;;;;PŘÍLOHY K JÍDLŮM
Křen;390;11;;;;PŘÍLOHY K JÍDLŮM
Máslo;395;13;;;;PŘÍLOHY K JÍDLŮM
Olivy;398;14;;;;PŘÍLOHY K JÍDLŮM
CSV;

        $csv = Reader::createFromString($csvData);
        $csv->setDelimiter(';');
        $csv->setHeaderOffset(0);
        $records = Statement::create()->process($csv);

        foreach ($records as $record) {
            DB::table('restaurant_menu_items')->insert([
                'restaurant_menu_id' => DB::table('restaurant_menus')->where('name', $record['category_name'])->value('id'),
                'number' => $record['number'],
                'name' => $record['name'],
                'description' => $record['text'],
                'weight' => $record['weight'],
                'order' => (int)$record['order'],
                'price' => $record['price'],
            ]);
        }


    }
}

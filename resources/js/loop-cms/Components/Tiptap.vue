<template>
    <div class="uk-textarea">
        <div class="uk-margin-small-bottom" v-if="editor">
            <div class="uk-grid uk-grid-small" uk-grid>
                <div>
                    <button class="uk-button uk-button-small uk-button-default"
                            @click="editor.chain().focus().setParagraph().run()"
                            :class="{ 'uk-active': editor.isActive('paragraph') }">
                        P
                    </button>
                </div>
                <div>
                    <button class="uk-button uk-button-small uk-button-default"
                            @click="editor.chain().focus().toggleHeading({ level: 1 }).run()"
                            :class="{ 'uk-active': editor.isActive('heading', { level: 1 }) }">
                        H1
                    </button>
                </div>
                <div>
                    <button class="uk-button uk-button-small uk-button-default"
                            @click="editor.chain().focus().toggleHeading({ level: 2 }).run()"
                            :class="{ 'uk-active': editor.isActive('heading', { level: 2 }) }">
                        H2
                    </button>
                </div>
                <div>
                    <button class="uk-button uk-button-small uk-button-default"
                            @click="editor.chain().focus().toggleHeading({ level: 3 }).run()"
                            :class="{ 'uk-active': editor.isActive('heading', { level: 3 }) }">
                        H3
                    </button>
                </div>
                <div>
                    <button class="uk-button uk-button-small uk-button-default"
                            @click="editor.chain().focus().toggleBold().run()"
                            :disabled="!editor.can().chain().focus().toggleBold().run()"
                            :class="{ 'uk-active': editor.isActive('bold') }">
                        <span uk-icon="icon: bold;"></span>
                    </button>
                </div>
                <div>
                    <button class="uk-button uk-button-small uk-button-default"
                            @click="editor.chain().focus().toggleItalic().run()"
                            :disabled="!editor.can().chain().focus().toggleItalic().run()"
                            :class="{ 'uk-active': editor.isActive('italic') }">
                        <span uk-icon="icon: italic;"></span>
                    </button>
                </div>
                <div>
                    <button class="uk-button uk-button-small uk-button-default"
                            @click="editor.chain().focus().toggleStrike().run()"
                            :disabled="!editor.can().chain().focus().toggleStrike().run()"
                            :class="{ 'uk-active': editor.isActive('strike') }">
                        <span uk-icon="icon: strikethrough;"></span>
                    </button>
                </div>
                <div>
                    <button class="uk-button uk-button-small uk-button-default"
                            @click="editor.chain().focus().toggleBulletList().run()"
                            :class="{ 'uk-active': editor.isActive('bulletList') }">
                        <span uk-icon="icon: list;"></span>
                    </button>
                </div>
                <div>
                    <button class="uk-button uk-button-small uk-button-default"
                            @click="editor.chain().focus().toggleOrderedList().run()"
                            :class="{ 'uk-active': editor.isActive('orderedList') }">
                        1,2,3...
                    </button>
                </div>
                <div>
                    <button class="uk-button uk-button-small uk-button-default"
                            @click="editor.chain().focus().toggleBlockquote().run()"
                            :class="{ 'uk-active': editor.isActive('blockquote') }">
                        <span uk-icon="icon:  quote-right;"></span>
                    </button>
                </div>
                <div>
                    <button class="uk-button uk-button-small uk-button-default"
                            @click="editor.chain().focus().undo().run()"
                            :disabled="!editor.can().chain().focus().undo().run()">
                        <span uk-icon="icon: reply;"></span>
                    </button>
                </div>
                <div>
                    <button class="uk-button uk-button-small uk-button-default"
                            @click="editor.chain().focus().redo().run()"
                            :disabled="!editor.can().chain().focus().redo().run()">
                        <span uk-icon="icon: forward;"></span>
                    </button>
                </div>
            </div>
        </div>
        <editor-content :editor="editor"/>
    </div>
</template>
<script>
import StarterKit from '@tiptap/starter-kit'
import {Editor, EditorContent} from '@tiptap/vue-3'
import Document from '@tiptap/extension-document'
import Paragraph from '@tiptap/extension-paragraph'
import Text from '@tiptap/extension-text'

export default {
    components: {
        EditorContent,
    },

    props: {
        modelValue: {
            type: String,
            default: '',
        },
    },

    emits: ['update:modelValue'],

    data() {
        return {
            editor: null,
        }
    },

    watch: {
        modelValue(value) {
            // HTML
            const isSame = this.editor.getHTML() === value

            // JSON
            // const isSame = JSON.stringify(this.editor.getJSON()) === JSON.stringify(value)

            if (isSame) {
                return
            }

            this.editor.commands.setContent(value, false)
        },
    },

    mounted() {
        this.editor = new Editor({

            // element: document.querySelector('.element'),
            extensions: [
                StarterKit,
            ],
            // autofocus: true,
            editable: true,
            // injectCSS: false,


            content: this.modelValue,
            onUpdate: () => {
                // HTML
                this.$emit('update:modelValue', this.editor.getHTML())

                // JSON
                // this.$emit('update:modelValue', this.editor.getJSON())
            },
        })
    },

    beforeUnmount() {
        this.editor.destroy()
    },
}
</script>
<style lang="scss">
.uk-button.uk-active {
    background-color: lightgray;
}

.ProseMirror {
    min-height: 200px
}

.tiptap {
    .tiptap {

        min-height: 100px;

        > * + * {
            margin-top: 0.75em;
        }

        ul,
        ol {
            padding: 0 1rem;
        }

        h1,
        h2,
        h3,
        h4,
        h5,
        h6 {
            line-height: 1.1;
        }

        code {
            background-color: rgba(#616161, 0.1);
            color: #616161;
        }

        pre {
            background: #0D0D0D;
            color: #FFF;
            font-family: 'JetBrainsMono', monospace;
            padding: 0.75rem 1rem;
            border-radius: 0.5rem;

            code {
                color: inherit;
                padding: 0;
                background: none;
                font-size: 0.8rem;
            }
        }

        img {
            max-width: 100%;
            height: auto;
        }

        blockquote {
            padding-left: 1rem;
            border-left: 2px solid rgba(#0D0D0D, 0.1);
        }

        hr {
            border: none;
            border-top: 2px solid rgba(#0D0D0D, 0.1);
            margin: 2rem 0;
        }
    }

}
</style>

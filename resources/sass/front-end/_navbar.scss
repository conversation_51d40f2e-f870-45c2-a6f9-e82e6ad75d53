// Navbar styling

// Variables
$navbar-height: 80px;
$navbar-mobile-height: 60px;
$bhm-gold: #bd9f57;

// Common navbar styles
.nav-mode-black, .nav-mode-white {
  transition: background-color 0.3s ease;
}

// Black navigation mode
.nav-mode-black {
  .uk-navbar-container {
    background-color: rgba(255, 255, 255, 0.95);
  }

  .uk-navbar-toggle-icon {
    color: #000;
  }
}

// White navigation mode
.nav-mode-white {
  .uk-navbar-container {
    background-color: transparent;
  }

  .uk-navbar-toggle-icon {
    color: #fff;
  }
}

// Mobile navbar
.bhm-header-mobile {
  .uk-navbar-container {
    min-height: $navbar-mobile-height;
  }

  .uk-offcanvas-bar {
    background-color: #fff;

    .uk-nav-primary > li > a {
      color: #333;
      font-size: 18px;
      padding: 10px 0;

      &:hover {
        color: $bhm-gold;
      }
    }

    .uk-offcanvas-close {
      color: #333;
    }
  }
}

// Desktop navbar
.uk-visible\@s {
  .uk-navbar-container {
    min-height: $navbar-height;
  }

  .bhm-navbar-toggle {
    margin-left: 20px;
  }

  // Language switcher
  .uk-grid-small {
    margin-right: 15px;

    a {
      color: inherit;
      font-weight: 500;

      &.uk-badge {
        background-color: $bhm-gold;
        color: #fff;
        padding: 0 10px;
        border-radius: 12px;
      }
    }
  }
}

// Navigation menu
#bhm-menu-dialog {
  .uk-nav-primary {
    li {
      margin-bottom: 10px;

      a {
        transition: color 0.3s ease;

        &:hover {
          color: $bhm-gold;
          text-decoration: none;
        }
      }

      &.uk-active {
        a {
          color: $bhm-gold;
          font-weight: 600;
          position: relative;

          &:after {
            content: '';
            position: absolute;
            left: -15px;
            top: 50%;
            transform: translateY(-50%);
            width: 5px;
            height: 5px;
            border-radius: 50%;
            background-color: $bhm-gold;
          }
        }
      }
    }
  }
}

// Fullscreen navigation modal
#bhm-nav {
  .uk-modal-dialog {
    background-color: rgba(255, 255, 255, 0.98);
  }

  .bhm-nav-close {
    color: #333;
  }

  .bhm-nav {
    .bhm-nav-item {
      margin: 10px 0;

      a {
        color: #333;
        font-size: 24px;
        font-weight: 500;
        transition: color 0.3s ease;

        &:hover {
          color: #bd9f57;
          text-decoration: none;
        }
      }
    }
  }
}

// Sticky navbar
.nav-sticky {
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

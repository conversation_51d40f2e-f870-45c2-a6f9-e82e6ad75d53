const mix = require('laravel-mix');

/*
 |--------------------------------------------------------------------------
 | Mix Asset Management
 |--------------------------------------------------------------------------
 |
 | Mix provides a clean, fluent API for defining some Webpack build steps
 | for your Laravel applications. By default, we are compiling the CSS
 | file for the application as well as bundling up all the JS files.
 |
 */

/*
mix.js('resources/js/app.js', 'public/js').vue()
    /!*.postCss('resources/css/app.css', 'public/css', [
        require('postcss-import'),
        require('tailwindcss'),
    ])*!/

    .sass('resources/sass/app.scss', 'public/css')
    .webpackConfig(require('./webpack.config'))
    .version();

if (mix.inProduction()) {
    mix.version();
}


*/


mix
    .copyDirectory('resources/assets', 'public/')

    .js('resources/js/front-end/app.js', 'public/js/front-end')
    .js('resources/js/loop-cms/app.js', 'public/js/loop-cms').vue()
    //.extract(['vue', 'inertia'])
    //.sourceMaps()
    .sass('resources/sass/front-end/app.scss', 'public/css/front-end')
    .sass('resources/sass/loop-cms/app.scss', 'public/css/loop-cms')
    .webpackConfig(require('./webpack.config'))
    // .browserSync({
    //     // browse to http://localhost:3000/ during development,
    //     // ./public directory is being served
    //     host: 'cms.test',
    //     port: 3000,
    //
    // })
    //.version()
;


if (mix.inProduction()) {
    mix.version();
}



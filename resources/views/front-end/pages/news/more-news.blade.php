<div class="uk-grid-margin uk-container">
    <div class="tm-grid-expand uk-child-width-1-1 uk-grid uk-grid-stack" uk-grid="">
        <div class="uk-width-1-1@m uk-first-column">
            <hr class="uk-margin-large">
            <h2 class="uk-margin-medium">{{ app()->getLocale() == 'cs' ? 'Dal<PERSON><PERSON> novinky' : 'More news' }}</h2>
            <div class="uk-margin uk-margin-remove-bottom">
                <div
                    class="uk-child-width-1-1 uk-child-width-1-2@s uk-child-width-1-4@l uk-grid-medium uk-grid-match uk-grid"
                    uk-grid uk-height-match="target: > div > .news-card">
                    @foreach($relatedNews as $item)
                        <div>
                            @php
                                $slugField = 'slug_' . app()->getLocale();
                                $slug = isset($item[$slugField]) && !empty($item[$slugField])
                                    ? $item[$slugField]
                                    : ($item['slug_en'] ?? $item['slug_cs']);
                            @endphp
                            <a class="news-card uk-link-toggle uk-display-block uk-scrollspy-inview"
                               href="{{ $item['type'] === 'blog' ? route(locale().'.news.blog.show', $slug) : ($item['has_detail'] ? route(locale().'.news.show', $slug) : '#') }}"
                               aria-label="Read more" uk-scrollspy-class="">
                                <div class="news-card-media">
                                    <img
                                        src="{{ $item['type'] === 'blog' ? '/images/blog/th/'.($item['img'] ?? $slug.'.jpg') : '/images/'.$item['type'].'/th/'.$slug.'.jpg' }}"
                                        alt="" loading="lazy">
                                </div>
                                <div class="news-card-content">
                                    <div class="news-card-meta">
                                        <div class="news-card-date">
                                            @php
                                                $date = \Carbon\Carbon::parse($item['date']);
                                                if(app()->getLocale() == 'cs') {
                                                    $dateFormatted = $date->locale('cs')->isoFormat('D. MMMM YYYY');
                                                } else {
                                                    $dateFormatted = $date->locale('en')->isoFormat('MMMM D, YYYY');
                                                }
                                            @endphp
                                            {{ $dateFormatted }}
                                        </div>
                                        <span
                                            class="news-card-type type-{{ $item['type'] === 'blog' ? 'blog' : 'news' }}">
                                        {{ $item['type'] === 'blog' ? __('news.blog') : __('news.tz') }}
                                    </span>
                                    </div>
                                    <h3 class="news-card-title">
                                        {{ $item['title_'.app()->getLocale()] ?: $item['title_en'] }}
                                    </h3>
                                </div>
                            </a>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
    </div>
</div>

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class RestaurantDailyMenuItem extends Model
{
    use HasFactory;

    public const TYPE = [
        1=>"POLÉVKY - podáváme do 18:00",
        3=>'HLAVNÍ JÍDLA - podávaná do 15:00',
        4=>'HLAVNÍ JÍDLA - minutková úprava',
        10=>"Salát",
        12=>"Dezert",
    ];

    public $timestamps = false;

    public function menu(): BelongsTo
    {
        return $this->belongsTo(RestaurantDailyMenu::class);
    }
}

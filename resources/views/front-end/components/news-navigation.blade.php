<!-- Main tabs for News vs Media Coverage with Media Contact on the right -->
<div class="uk-margin-medium-bottom uk-position-relative news-nav-container">
    <div class="uk-flex uk-flex-wrap uk-flex-center uk-flex-middle news-nav-flex">
        <ul class="uk-tab uk-flex-center uk-margin-remove-bottom">
            <li class="{{ request()->routeIs(locale().'.news') || request()->routeIs(locale().'.news.blog') ? 'uk-active' : '' }}">
                <a href="{{ route(locale().'.news') }}">{{ __('news.news') }}</a>
            </li>
            <li class="{{ request()->routeIs(locale().'.news.press') ? 'uk-active' : '' }}">
                <a href="{{ route(locale().'.news.press') }}">{{ __('news.media_coverage') }}</a>
            </li>
        </ul>
        <a href="{{ route(locale().'.media-contact') }}" class="media-contact-link uk-text-primary {{ request()->routeIs(locale().'.media-contact') ? 'uk-active' : '' }}">
            <span uk-icon="icon: receiver; ratio: 0.8" class=""></span>
            <span class="media-contact-text">{{ __('news.contact_for_media') }}</span>
        </a>
    </div>
</div>

@if(request()->routeIs(locale().'.news') || request()->routeIs(locale().'.news.blog'))
<!-- Secondary filter for News types -->
<div class="news-filter">
    <ul class="uk-subnav uk-subnav-pill uk-flex-center uk-flex-wrap news-filter-nav">
        <li class="{{ request()->routeIs(locale().'.news') && !request()->query('filter') || request()->query('filter') === 'all' ? 'uk-active' : '' }}">
            <a href="{{ route(locale().'.news') }}">{{ __('news.all_news') }}</a>
        </li>
        <li class="{{ request()->query('filter') === 'news' ? 'uk-active' : '' }}">
            <a href="{{ route(locale().'.news', ['filter' => 'news']) }}">{{ __('news.press_releases') }}</a>
        </li>
        <li class="{{ request()->routeIs(locale().'.news.blog') || request()->query('filter') === 'blog' ? 'uk-active' : '' }}">
            <a href="{{ route(locale().'.news', ['filter' => 'blog']) }}">{{ __('news.blog') }}</a>
        </li>
    </ul>
</div>
@endif

// Portfolio page styles
.portfolio-page {
    background-image: url('/images/home/<USER>');
    background-size: 5000px;
    background-position: center center;

    @media (max-width: 767px) {
        //background-size: 600%;
    }
}

// Card styles
.portfolio-card {
    height: 100%;
    display: flex;
    flex-direction: column;
    transition: all 0.2s ease;
    background-color: #f9f9f9;
    backdrop-filter: blur(4px);
    border: 1px solid #bd9f5740;
    border-radius: 10px;
    overflow: hidden;
    position: relative;

    &:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
    }
}

.portfolio-card-flag {
    position: absolute;
    top: 15px;
    right: 15px;
    z-index: 2;
}

.country-flag-icon {
    border-radius: 50%;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(189, 158, 87, 0.7);
}

.portfolio-card-logo-container {
    height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    margin-bottom: 0;
    position: relative;
    background: #fff;
    border-bottom: 1px solid #bd9f5740;
}

.portfolio-card-logo {
    max-height: 80px;
    max-width: 60%;
    object-fit: contain;
    //filter: grayscale(100%);
}

.portfolio-card-industry-tag {
    text-align: center;
    padding: 5px 10px;

    position: relative;
    top: -16px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #bd9f5740;
    display: inline-block;
    max-width: 80%;
    background-color: rgb(189, 164, 106);
    color: #fffafa;
}

// Single color for all industry tags
.industry-medtech,
.industry-cleantech,
.industry-renewables,
.industry-tech-startups,
.industry-hospitality,
.industry-residentials,
.industry-logistics,
.industry-retail,
.industry-other {

}

.portfolio-card-content {
    padding: 0 20px 20px;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.portfolio-card-title {
    font-size: 23px;
    //letter-spacing: 0.02em;
    font-weight: 500;
    margin-bottom: 10px;
    padding-bottom: 5px;
    text-align: center;
    color: #656565 !important;
}

.portfolio-card-label {
    display: inline-block;
    font-size: 0.8rem;
    padding: 4px 10px;
    margin-right: 0;
    margin-bottom: 5px;
    border-radius: 3px;
    background-color: #f5f5f5;
    color: $bhm-dark-gray;
}

.country-flag-icon {
    border-radius: 50%;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

// Modal styles
.portfolio-modal-content {
    padding: 30px;
    position: relative;
}

.portfolio-modal-header {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    //margin-bottom: 20px;
}

.portfolio-modal-logo-wrapper {
    display: flex;
    align-items: flex-start;
    justify-content: flex-start;
    position: relative;
    width: 100%;
    margin: 80px 0;
}

.portfolio-modal-logo {
    max-height: 80px;
    max-width: 200px;
    //filter: grayscale(100%);
}

.portfolio-modal-meta {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;
    //padding-bottom: 80px;
}

.portfolio-modal-flag {
    margin-right: 0;
}

.portfolio-modal-industry-tag {
    display: inline-block;
    padding: 6px 15px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    text-align: center;

    background-color: #f9f9f9;
    color: #656565;
}

.portfolio-modal-title-container {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    width: 100%;
}

.portfolio-modal-title {
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 10px;
    color: $bhm-dark-gray;
    text-align: left;
    flex: 1;
}

.portfolio-modal-country-name {
    display: block;
    text-align: center;
    font-size: 1rem;
    color: $bhm-dark-gray;
}

.portfolio-modal-description {
    margin: 20px 0;
    line-height: 1.6;
}

.portfolio-modal-image-container {
    padding: 30px;
    display: flex;
    align-items: center;
    justify-content: center;

    @media (min-width: 640px) {
        height: 100vh; /* Full viewport height on desktop */
    }
}

.portfolio-modal-image {
    height: auto;
    max-height: 90%;
    object-fit: contain;
    width: auto;
    max-width: 90%;
}

.portfolio-modal-buttons {
    margin-top: 30px;
}

.portfolio-modal-button {
    margin-right: 10px;
    margin-bottom: 10px;
}

// Responsive adjustments
@media (max-width: 959px) {
    .portfolio-card-logo-container {
        height: 100px;
    }

    .portfolio-card-logo {
        max-height: 70px;
        max-width: 95%;
    }

    .portfolio-card-flag {
        top: 8px;
        right: 8px;
    }

    .country-flag-icon {
        width: 20px;
        height: 20px;
    }

    .portfolio-card-industry-tag {
        font-size: 0.7rem;
        padding: 4px 8px;
        margin: 1px 15px 5px;
    }

    .portfolio-card-content {
        margin-top: -15px;
        padding-bottom: 10px;
    }

    .portfolio-card-title {
        font-size: 1rem;
        padding: 0;
    }

    .portfolio-modal-content {
        padding: 20px;
    }

    .portfolio-modal-logo-wrapper {
        align-items: center;
        justify-content: center;
        margin: 40px 0;
    }

    .portfolio-modal-logo {
        max-width: 160px;
    }

    .portfolio-modal-meta {
        justify-content: center;
        width: 100%;
    }

    .portfolio-modal-flag {
        margin-right: 0;
    }

    .portfolio-modal-title-container {
        flex-direction: row;
        align-items: center;
    }

    .portfolio-modal-industry-tag {
        font-size: 0.8rem;
        padding: 5px 12px;
    }

    .portfolio-modal-title {
        font-size: 1.6rem;
    }

    .portfolio-modal-image-container {
        padding: 15px;
        min-height: 250px;
        height: auto; /* Override desktop height on mobile */
    }

    .portfolio-modal-image {
        max-height: 85%;
        max-width: 85%;
    }
}

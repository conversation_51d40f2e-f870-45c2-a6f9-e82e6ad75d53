<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>Menu</title>
    <style>
        @font-face {
            font-family: 'R';
            src: url({{ storage_path("fonts/RobotoSlab-Light.ttf") }}) format("truetype");
            font-weight: 500;
            font-style: normal;
        }

        @font-face {
            font-family: 'R';
            src: url({{ storage_path("fonts/RobotoSlab-SemiBold.ttf") }}) format("truetype");
            font-weight: 700;
            font-style: normal;
        }

        @page {
            margin: 20mm 24mm 20mm 24mm;
        }

        .page {
            width: 210mm; /* A4 width */
            /*height: 297mm; !* A4 height *!*/

            box-sizing: border-box;
        }

        body {
            font-family: R, serif;
            font-weight: 500;
            margin: 0;
            padding: 0;
            font-size: 14px;
            line-height: 14px;
        }

        td {
            vertical-align: top;
        }

        .category-title {
            font-weight: normal;
            font-size: 14px;
            padding-top: 5mm;
        }

        .food-number {
            width: 13mm;
            font-size: 12px;
        }

        .food-weight {
            width: 12mm;
            font-size: 12px;
        }

        .food-name {
            font-weight: 700;
            padding-right: 5mm;
        }

        .food-price {
            width: 20mm;
        }

        .food-box {
            width: 130mm;
            padding-bottom: 2mm;
        }

        .food-description {
            font-size: 11px;
        }

        .category {
            page-break-inside: avoid;
        }
    </style>
</head>
<body>
<div class="page ">
        <h2 style="margin-left: 28mm;">Denní nabídka ({{$menu}})</h2>


    @foreach($categories as $type => $items)

        <div class="category">
            <table>
                <tr>
                    <td class="food-number"></td>
                    <td class="food-weight"></td>
                    <td>
                        <div class="category-title">{{$category_label[$date][$type]}}</div>
                    </td>
                    <td class="food-price"></td>
                </tr>
            </table>
            @foreach($items as $food)
                <table>
                    <tr>
                        <td class="food-number">{{ $food->number }}</td>
                        <td class="food-weight">{{ $food->weight }}</td>
                        <td class="food-box">
                            <div class="food-name">{{ $food->name }}</div>
                            <div class="food-description">{{ $food->description }}</div>
                        </td>
                        <td class="food-price">{{ $food->price }}</td>
                    </tr>
                </table>
            @endforeach
        </div>
    @endforeach
</div>
</body>
</html>

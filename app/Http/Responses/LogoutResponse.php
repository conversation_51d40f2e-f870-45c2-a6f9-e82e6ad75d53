<?php

namespace App\Http\Responses;

use Illuminate\Http\Request;
use Inertia\Inertia;
use <PERSON><PERSON>\Fortify\Contracts\LogoutResponse as LogoutResponseContract;
use Symfony\Component\HttpFoundation\Response;

class LogoutResponse implements LogoutResponseContract
{
    /**
     * Create an HTTP response that represents the object.
     *
     * @param Request $request
     *
     * @return Response
     */
    public function toResponse($request)
    {

        /*
         * https://laravelshowcase.com/question/delete-cookies-on-logout-laravel-8-x-fortify-jetstream
        return $request->wantsJson()
            ? new JsonResponse('', 204)
            : redirect('www.example.com');
        */

        return Inertia::location(route('login'));
    }
}

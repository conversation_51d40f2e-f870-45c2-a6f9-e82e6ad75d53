import:
  - recipe/laravel.php
  - contrib/npm.php

config:
  repository: '******************:bhmgroup.eu.git'
  keep_releases: 2
  bin/php: "/usr/bin/php8.1"

hosts:
  prod:
    remote_user: bhmgroup_eu
    hostname: 'draco.keyup.cz'
    deploy_path: '/var/www/bhmgroup.eu/web'

tasks:
  deploy:
    - deploy:prepare
    - deploy:vendors
    - artisan:storage:link
    - artisan:view:cache
    - artisan:config:cache
    - artisan:route:cache
#    - artisan:migrate
    - artisan:optimize
    - npm:install
    - npm:run:prod
    - deploy:publish
  npm:run:prod:
    - run: 'cd {{release_path}} && npm run production'

after:
  deploy:failed: deploy:unlock

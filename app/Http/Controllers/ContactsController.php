<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class ContactsController extends Controller
{
    public function index()
    {
        // Language (for now hardcoded, future would be from session or URL)
        $lang = locale(); // or 'cs' for Czech

        // Company information
        $companyInfo = [
            'cs' => [
                'address' => 'BHM group a.s.<br/>Ovocný trh 1096/8<br/><PERSON><PERSON><br/>110 00, Praha 1',
                'contact' => '<ul class="uk-list" style="margin-top: 10px">
                                <li><a href="tel:+420234002320">+*********** 320</a></li>
                                <li><a href="mailto:<EMAIL>"><EMAIL></a></li>
                             </ul>',
                'legal' => 'IČ: 02865998<br/>DIČ: **********',
                'registry' => 'Společnost je zapsaná v obchodním rejstříku vedeném u Městského soudu v Praze, oddíl B, vložka 20781',
                'title' => 'Kontakty',
                'company_address' => 'Sídlo společnosti:',
                'contact_info' => 'Kontaktní údaje:',
                'company_info' => 'Informace o společnosti:',
                'code_of_conduct' => 'Etický kodex',
                'anti_bribery' => 'Zásady proti úplatkářství a korupci'
            ],
            'en' => [
                'address' => 'BHM group a.s.<br/>Ovocný trh 1096/8<br/>Staré Město<br/>110 00, Prague 1',
                'contact' => '<ul class="uk-list" style="margin-top: 10px">
                                <li><a href="tel:+420234002320">+*********** 320</a></li>
                                <li><a href="mailto:<EMAIL>"><EMAIL></a></li>
                             </ul>',
                'legal' => 'ID: 02865998<br/>VAT ID: **********',
                'registry' => 'The company is registered in the Commercial Register kept by the Municipal Court in Prague, Section B, File No. 20781',
                'title' => 'Contacts',
                'company_address' => 'Company Headquarters:',
                'contact_info' => 'Contact Information:',
                'company_info' => 'Company Information:',
                'code_of_conduct' => 'Code of Conduct',
                'anti_bribery' => 'Anti-Bribery and Anti-Corruption Policy'
            ]
        ];

        // Menu items (from HomeController)
        $menuItems = [
            [
                'title' => [
                    'en' => 'About Us',
                    'cs' => 'O nás'
                ],
                'url' => '#'
            ],
            [
                'title' => [
                    'en' => 'Team',
                    'cs' => 'Tým'
                ],
                'url' => '#'
            ],
            [
                'title' => [
                    'en' => 'Portfolio',
                    'cs' => 'Portfolio'
                ],
                'url' => '#'
            ],
            [
                'title' => [
                    'en' => 'Press Room',
                    'cs' => 'Press Room'
                ],
                'url' => '#'
            ],
            [
                'title' => [
                    'en' => 'Careers',
                    'cs' => 'Kariéra'
                ],
                'url' => '#'
            ],
            [
                'title' => [
                    'en' => 'Contact',
                    'cs' => 'Kontakt'
                ],
                'url' => '#'
            ]
        ];

        // Map coordinates
        $mapCoordinates = [
            'lat' => 50.086082,
            'lng' => 14.424558
        ];

        return view('front-end.pages.contacts', compact(
            'lang',
            'companyInfo',
            'menuItems',
            'mapCoordinates'
        ));
    }
}

<script setup>
defineProps({
    message: {
        type: String,
        required: true
    },
    length: {
        type: Number,
        default: 240
    },
});

const stripHtmlTagsRegex = (htmlString, textLimit) => {
    const doc = new DOMParser().parseFromString(htmlString, 'text/html');
    let rawText = doc.body.textContent;

    return (rawText.length > textLimit) ? rawText.slice(0, textLimit - 1) + '...' : rawText || '';
};
</script>
<template>
    {{ stripHtmlTagsRegex(message, length) }}
</template>

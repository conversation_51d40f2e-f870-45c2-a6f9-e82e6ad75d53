@extends('front-end.layouts.app')

@section('title', __('navigation.contacts'))

@section('content')
    <div class="uk-section-default  uk-padding-remove-bottom"
         style="background-image: url('/images/home/<USER>'); background-size: 4000px; background-position: 25% center;"
         uk-scrollspy="target: [uk-scrollspy-class]; cls: uk-animation-fade; delay: false;">
        @include('front-end.components.navbar', ['navMode' => 'black'])

        <div class="uk-section">
            <div class="uk-container uk-container-large">
                <div class="uk-grid uk-margin-large-top">
                    <div class="uk-width-1-1 uk-padding-remove-top">
                        <h2 class="uk-heading-small uk-margin-bottom uk-text-uppercase">
                            {{ $companyInfo[$lang]['title'] }}
                        </h2>


                        <div class="uk-grid uk-child-width-1-4@s" uk-grid>
                            <div>
                                <div class="uk-panel uk-margin-remove-first-child">
                                    <div class="uk-panel uk-margin-medium-top">
                                        <p class="uk-text-emphasis">
                                            <strong style="font-weight: 600">{{ $companyInfo[$lang]['company_address'] }}</strong><br/>
                                            {!! $companyInfo[$lang]['address'] !!}
                                        </p>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <div class="uk-panel uk-margin-remove-first-child">
                                    <div class="uk-panel uk-margin-medium-top">
                                        <strong style="font-weight: 600">{{ $companyInfo[$lang]['contact_info'] }}</strong><br/>
                                        {!! $companyInfo[$lang]['contact'] !!}
                                    </div>
                                </div>
                            </div>
                            <div class="uk-width-1-2@s">
                                <div class="uk-panel uk-margin-remove-first-child">
                                    <p class="uk-text-emphasis">
                                        <strong style="font-weight: 600">{{ $companyInfo[$lang]['company_info'] }}</strong><br/>
                                        {!! $companyInfo[$lang]['legal'] !!}<br/>
                                    </p>
                                    <p class="uk-text-small uk-width-large">{{ $companyInfo[$lang]['registry'] }}</p>

                                    <p>
                                        <a href="{{ route(locale().'.code-of-conduct') }}">{{ $companyInfo[$lang]['code_of_conduct'] }}</a> |
                                        <a href="{{ route(locale().'.anti-bribery-and-anti-corruption-policy') }}">{{ $companyInfo[$lang]['anti_bribery'] }}</a>
                                    </p>
                                </div>
                            </div>
                            <div>
                                <div class="uk-panel uk-margin-remove-first-child">
                                    <div class="uk-panel uk-margin-medium-top">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div id="map-canvas"></div>
    </div>

    @include('front-end.components.footer')

    <style>
        #map-canvas {
            width: 100%;
            height: 50vh;
        }

        .pin-wrap {
            position: absolute;
            width: 60px;
            height: 60px;
            margin-left: -30px;
            margin-top: -60px;
        }

        .shadow {
            position: absolute;
            width: 40px;
            height: 10px;
            border-radius: 50%;
            background: rgba(0, 0, 0, 0.1);
            transform: rotateX(60deg);
            z-index: -1;
            margin-top: -10px;
            margin-left: -20px;
            left: 50%;
            top: 100%;
        }
    </style>

    <script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyA-KZRw18LnfjOW6iBII3jAnnjFbbdgiw8"></script>
    <script>
        // Initialize the map when the DOM is fully loaded
        document.addEventListener('DOMContentLoaded', function() {
            initMap();
        });

        // Map initialization function
        function initMap() {
            const mapElement = document.getElementById('map-canvas');
            if (!mapElement) return;

            // Map coordinates from the controller
            const markerLatLng = {
                lat: {{ $mapCoordinates['lat'] }},
                lng: {{ $mapCoordinates['lng'] }}
            };

            // Map styling
            const mapStyles = [
                {
                    "elementType": "geometry",
                    "stylers": [{ "color": "#f5f5f5" }]
                },
                {
                    "elementType": "labels.icon",
                    "stylers": [{ "visibility": "off" }]
                },
                {
                    "elementType": "labels.text.fill",
                    "stylers": [{ "color": "#616161" }]
                },
                {
                    "elementType": "labels.text.stroke",
                    "stylers": [{ "color": "#f5f5f5" }]
                },
                {
                    "featureType": "administrative.land_parcel",
                    "elementType": "labels.text.fill",
                    "stylers": [{ "color": "#bdbdbd" }]
                },
                {
                    "featureType": "poi",
                    "elementType": "geometry",
                    "stylers": [{ "color": "#eeeeee" }]
                },
                {
                    "featureType": "poi",
                    "elementType": "labels.text.fill",
                    "stylers": [{ "color": "#757575" }]
                },
                {
                    "featureType": "poi.park",
                    "elementType": "geometry",
                    "stylers": [{ "color": "#e5e5e5" }]
                },
                {
                    "featureType": "poi.park",
                    "elementType": "labels.text.fill",
                    "stylers": [{ "color": "#9e9e9e" }]
                },
                {
                    "featureType": "road",
                    "elementType": "geometry",
                    "stylers": [{ "color": "#ffffff" }]
                },
                {
                    "featureType": "road.arterial",
                    "elementType": "labels.text.fill",
                    "stylers": [{ "color": "#757575" }]
                },
                {
                    "featureType": "road.highway",
                    "elementType": "geometry",
                    "stylers": [{ "color": "#dadada" }]
                },
                {
                    "featureType": "road.highway",
                    "elementType": "labels.text.fill",
                    "stylers": [{ "color": "#616161" }]
                },
                {
                    "featureType": "road.local",
                    "elementType": "labels.text.fill",
                    "stylers": [{ "color": "#9e9e9e" }]
                },
                {
                    "featureType": "transit.line",
                    "elementType": "geometry",
                    "stylers": [{ "color": "#e5e5e5" }]
                },
                {
                    "featureType": "transit.station",
                    "elementType": "geometry",
                    "stylers": [{ "color": "#eeeeee" }]
                },
                {
                    "featureType": "water",
                    "elementType": "geometry",
                    "stylers": [{ "color": "#c9c9c9" }]
                },
                {
                    "featureType": "water",
                    "elementType": "labels.text.fill",
                    "stylers": [{ "color": "#9e9e9e" }]
                }
            ];

            // Map options
            const mapOptions = {
                zoom: 15,
                scrollwheel: false,
                center: markerLatLng,
                mapTypeId: google.maps.MapTypeId.ROADMAP,
                disableDefaultUI: true,
                zoomControl: true,
                zoomControlOptions: {
                    position: google.maps.ControlPosition.RIGHT_TOP
                },
                styles: mapStyles
            };

            // Create the map
            const map = new google.maps.Map(mapElement, mapOptions);

            // Add custom marker
            new CustomMarker({
                position: markerLatLng,
                map: map,
                iconUrl: '/images/map-pin.png'
            });
        }

        /**
         * Custom Marker class that extends OverlayView
         */
        class CustomMarker extends google.maps.OverlayView {
            constructor(options) {
                super();
                this.position = options.position;
                this.iconUrl = options.iconUrl;
                this.setMap(options.map);
            }

            /**
             * Called when the overlay is added to the map
             */
            onAdd() {
                // Create main container
                this.div = document.createElement('div');

                // Create shadow element
                const shadow = document.createElement('div');
                shadow.className = 'shadow';
                this.div.appendChild(shadow);

                // Create pin wrapper
                const pinWrap = document.createElement('div');
                pinWrap.className = 'pin-wrap';
                this.div.appendChild(pinWrap);

                // Create pin with image
                const pin = document.createElement('div');
                pin.className = 'pin';
                pin.innerHTML = `<img src="${this.iconUrl}" style="width: 60px" />`;
                pinWrap.appendChild(pin);

                // Set position to absolute for proper placement
                this.div.style.position = 'absolute';

                // Add to the overlay pane
                const panes = this.getPanes();
                panes.overlayImage.appendChild(this.div);
            }

            /**
             * Draw the marker based on the current projection and zoom level
             */
            draw() {
                // Return if div hasn't been created yet
                if (!this.div) return;

                // Calculate pixel position
                const point = this.getProjection().fromLatLngToDivPixel(this.position);

                // Set the position of the marker
                if (point) {
                    this.div.style.left = point.x + 'px';
                    this.div.style.top = point.y + 'px';
                }
            }

            /**
             * Called when the overlay is removed from the map
             */
            onRemove() {
                if (this.div) {
                    this.div.parentNode.removeChild(this.div);
                    this.div = null;
                }
            }
        }
    </script>
@endsection

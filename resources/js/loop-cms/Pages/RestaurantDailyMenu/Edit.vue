<script setup>
import AppLayout from '@/Layouts/AppLayout.vue';
import {router, useForm} from '@inertiajs/vue3'
import UIkit from 'uikit';
import ActionMessage from '@/Components/ActionMessage';
import InputError from '@/Components/InputError';
import DateTime from '@/Components/DateTime';
import Date from '@/Components/Date';
import {ref, watch} from "vue";
import SearchInput from './SearchInput.vue';

const props = defineProps({
    result: Object,
});


const textTitle = 'Denní menu - ' + props.result.name;
const textDeleteConfirm = 'Opravdu chcete menu smazat?';

const routeUpdate = 'loop.restaurant-daily-menus.update';
const routeDestroy = 'loop.restaurant-daily-menus.destroy';


const newDateObj = {
    id: null,
    number: '',
    name: '',
    price: '',
    weight: '',
};

const form = useForm({
    id: props.result.id,
    dates: props.result.dates,
    category_labels: props.result.category_labels,
    search: '',
    _method: 'PUT',
});

function update() {
    form.post(route(routeUpdate, form.id), {
        preserveScroll: true,
        onSuccess: (response) => {
            form.images = response.props.result.images;
            UIkit.notification({message: 'Uloženo', status: 'success'});
        },
        onError: (errors) => {
            let message = '<p class="uk-text-small">Během ukládání došlo k jedné, nebo více chybám. </p>';
            Object.values(errors).forEach(value => {
                message += `<div><p>${value}</p></div>`;
            });

            UIkit.notification({message: message, status: 'danger'});
        }
    });
}

function destroy() {

    UIkit.modal.confirm(textDeleteConfirm).then(function () {
        form.delete(route(routeDestroy, form), {
            onSuccess: (response) => {
                UIkit.notification({message: 'Smazáno', status: 'success'});
            }
        });
    });

}


function addNewItem(category) {
    category.push(Object.assign({}, newDateObj))
}

function removeItem(date, index) {
    date.splice(index, 1);
}


function addItem(category, data) {
    category.push(data);
}
</script>
<template>
    <app-layout :title="textTitle">
        <template #header>
            <div
                class="uk-child-width-expand@s uk-margin-small-top uk-flex-middle"
                uk-grid
            >
                <div class="uk-width-expand">
                    <div
                        class="uk-child-width-expand@s uk-flex uk-flex-middle"
                        uk-grid
                    >
                        <div class="uk-width-expand">
                            <div v-if="form.id">
                                Naposledy uloženo
                                <date-time :date="result.updated_at"/>
                                uživatelem {{ result.editor.name }}
                            </div>
                        </div>
                    </div>
                </div>
                <div class="uk-width-auto">
                    <button
                        class="uk-button uk-button-default uk-margin-right"
                        :loading="false"
                        type="submit"
                        @click.prevent="update()"
                    >
                        Uložit
                    </button>
<!--                    <button-->
<!--                        class="uk-button uk-button-danger"-->
<!--                        type="submit"-->
<!--                        @click.prevent="destroy()"-->
<!--                    >-->
<!--                        <span uk-icon="icon: trash;"/>-->
<!--                    </button>-->
                </div>
            </div>
        </template>
        <form
            class="uk-form-stacked"
            @submit.prevent="update"
        >
            <div class="uk-margin">
                <div class="uk-form-controls">
                    <div
                        class="uk-margin-xlarge-bottom"
                        v-for="(date, rawDate) in form.dates"
                        v-bind="{date}"
                        :key="date.id">
                        <h3 class="uk-text-uppercase">
                            <date :date="rawDate"/>
                            <a class="uk-button uk-button-default uk-margin-left uk-margin-right" :href="route('restaurant-daily-menus.pdf', {'menu': form.id, 'date':rawDate})">Stáhnout menu PDF</a>

                            <button
                                class="uk-button uk-button-primary uk-margin-right uk-float-right"
                                :loading="false"
                                type="submit"
                                @click.prevent="update()"
                            >
                                Uložit
                            </button>
                        </h3>

                        <div
                            style="background-color: #efefef"
                            class="uk-margin-small uk-padding uk-margin-large-bottom"
                            v-for="(category, index) in result.categories"
                            v-bind="{category}"
                            :key="index">
                            <h4>
                                <input class="uk-input uk-width-1-2" type="text" placeholder="Číslo" v-model="form.category_labels[rawDate][index]">
                                <a class="uk-button uk-button-small uk-margin-left uk-button-default"
                                                  @click="addNewItem(date[index])"><span
                                uk-icon="icon: plus;"></span> Přidat zcela novou položku</a>

                            </h4>

                            <div
                                class="uk-margin-small"
                                v-for="(item, index2) in date[index]"
                                v-bind="{item}"
                                :key="index2">
                                <div class="uk-grid uk-child-width-expand@s uk-margin-small-bottom" uk-grid>
                                    <div class="uk-width-auto">
                                        <input class="uk-input" type="text" placeholder="Číslo" v-model="item.number"
                                               style="width: 80px;">
                                    </div>
                                    <div class="uk-width-auto">
                                        <input class="uk-input" type="text" placeholder="Váha" v-model="item.weight"
                                               style="width: 80px;">
                                    </div>
                                    <div class="uk-width-expand">
                                    <textarea class="uk-textarea" placeholder="Název" v-model="item.name"
                                              rows="2"></textarea>
                                    </div>
                                    <div class="uk-width-auto">
                                        <input class="uk-input" type="text" placeholder="Cena" v-model="item.price"
                                               style="width: 100px;">
                                    </div>
                                    <div class="uk-width-auto">
                                        <a class="uk-button uk-button-danger uk-button-small"
                                           @click="removeItem(date[index], index2)"> <span uk-icon="trash"></span> </a>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <search-input
                                    :category=index
                                    @select="addItem(date[index], $event)"
                                />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="">
                <ActionMessage
                    :on="form.recentlySuccessful"
                >
                    Uspěšně uloženo
                </ActionMessage>
                <button
                    class="uk-button uk-button-primary uk-button-large"
                    @click.prevent="update()"
                    :loading="false"
                    type="submit"
                >
                    Uložit
                </button>
            </div>
        </form>
    </app-layout>
</template>

.people-background {
    background-color: white;
    background-image: url('/images/home/<USER>');
    background-size: contain;
    background-position: 10% center;
    padding-top: 100px;

    @media (max-width: 959px) {
        background-image: url('/images/home/<USER>');
        background-size: 2500px;
    }

    @media (min-width: 960px) {
        background-size: 250vw;
    }
}

.circ {
    display: none; /* Hide the old circular mask */
}

.squircle-mask {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    mask-image: url('/images/squircle-mask.svg');
    -webkit-mask-image: url('/images/squircle-mask.svg');
    mask-size: 95% 95%;
    -webkit-mask-size: 95% 95%;
    mask-repeat: no-repeat;
    -webkit-mask-repeat: no-repeat;
    mask-position: center;
    -webkit-mask-position: center;
    background-color: white;
    pointer-events: none;
    z-index: 2;
}

.person-card-link:hover {
    cursor: pointer;
}

.person-card-link:hover .squircle-mask {
    background-color: #f8f8f8;
}


.person-card-link:hover .person-card-info {
    /*display: none;    */
}

.person-card-link .person-card-more-text {
    opacity: 0;
}

.person-card-link:hover .person-card-more-text {
    opacity: 1;
    /*transition: all 0.2s ease-in 0.4s;*/

}

.person-card-more-text a {
    color: #333;
}


.circ-img {
    /*margin-top: 10%*/
    transform-origin: 50% -20%;
    transform: scale(1.2);
    transition: all 0.4s ease-out;
    position: absolute;
    z-index: 1;
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
}

.circ-img.krsek {
    transform-origin: 70% -25%;
}

.circ-img.cerny {
    transform-origin: 50% -27%;
}

.circ-img.zychova {
    transform-origin: 10% -25%;
}

.circ-img.kacena {
    transform-origin: 60% -20%;
}

.circ-img.mynar {
    transform-origin: 34% -26%;
}

.circ-img.rubes {
    transform-origin: 45% -30%;
}

.circ-img.bokor {
    transform-origin: 40% -30%;
}

.circ-img.vladar {
    transform-origin: 60% -20%;
}

.circ-img.chaloupka {
    transform-origin: 70% -30%;
}

.circ-img.kotyza {
    transform-origin: 35% -20%;
}

.circ-img.betkova {
    transform-origin: 50% -30%;
}

.circ-img.izak {
    transform-origin: 110% -30%;
}

.circ-img.stranovsky {
    transform-origin: 55% -30%;
}

.circ-img.harant {
    transform-origin: 65% -30%;
}

.circ-img.novacek {
    transform-origin: 50% -24%;
}

.circ-img.zadrapa {
    transform-origin: 70% -34%;
}

.circ-img.marklin {
    transform-origin: 25% -34%;
}

.circ-img.michal-prause {
    transform-origin: 40% -40%;
}

.circ-img.lucie-dofkova {
    transform-origin: -14% -32%;
}

.circ-img.robert-doucha {
    transform-origin: 50% 22%;
    transform: scale(0.72);
}

.circ-img.tuomas-hooli {
    transform-origin: 64% 53%;
    transform: scale(0.85);
}

.circ-img.markus-keussen {
    transform-origin: 34% -35%;
}

.circ-img.jan-svejda {
    transform-origin: 54% -30%;
}

.circ-img.peter-siso {
    transform-origin: 77% -31%;
}

.circ-img.miroslav-tabernaus {
    transform-origin: 21% -30%;
}

.circ-img.filip-holubovsky {
    transform-origin: 45% -35%;
}

.circ-img.cerna {
    transform-origin: 45% -38%;
}

.circ-img.kula {
    transform-origin: 25% -30%;
}

.circ-img.liskova {
    transform-origin: 135% -28%;
}

.circ-img.sanitrik {
    transform-origin: 55% -33%;
}


.circ-img.tison {
    transform-origin: 42% -37%;
}
.circ-img.takimci {
    transform-origin: 52% -27%;
}

.person-card-link .uk-position-center {
    display: none;
}

.person-card-link:hover .uk-position-center {
    display: block;
}


.bhm-team-textbox {
    z-index: -2;
}

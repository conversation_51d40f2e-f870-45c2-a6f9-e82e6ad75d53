{"codeToName": {"13": "CR", "32": "space", "33": "exclam", "34": "quotedbl", "35": "numbersign", "36": "dollar", "37": "percent", "38": "ampersand", "39": "<PERSON><PERSON><PERSON>", "40": "parenleft", "41": "parenright", "42": "asterisk", "43": "plus", "44": "comma", "45": "hyphen", "46": "period", "47": "slash", "48": "zero", "49": "one", "50": "two", "51": "three", "52": "four", "53": "five", "54": "six", "55": "seven", "56": "eight", "57": "nine", "58": "colon", "59": "semicolon", "60": "less", "61": "equal", "62": "greater", "63": "question", "64": "at", "65": "A", "66": "B", "67": "C", "68": "D", "69": "E", "70": "F", "71": "G", "72": "H", "73": "I", "74": "J", "75": "K", "76": "L", "77": "M", "78": "N", "79": "O", "80": "P", "81": "Q", "82": "R", "83": "S", "84": "T", "85": "U", "86": "V", "87": "W", "88": "X", "89": "Y", "90": "Z", "91": "bracketleft", "92": "backslash", "93": "bracketright", "94": "asciicircum", "95": "underscore", "96": "grave", "97": "a", "98": "b", "99": "c", "100": "d", "101": "e", "102": "f", "103": "g", "104": "h", "105": "i", "106": "j", "107": "k", "108": "l", "109": "m", "110": "n", "111": "o", "112": "p", "113": "q", "114": "r", "115": "s", "116": "t", "117": "u", "118": "v", "119": "w", "120": "x", "121": "y", "122": "z", "123": "braceleft", "124": "bar", "125": "braceright", "126": "asciitilde", "160": "uni00A0", "161": "exclamdown", "162": "cent", "163": "sterling", "164": "currency", "165": "yen", "166": "brokenbar", "167": "section", "168": "<PERSON><PERSON><PERSON>", "169": "copyright", "170": "ordfeminine", "171": "guillemotleft", "172": "logicalnot", "174": "registered", "175": "macron", "176": "degree", "177": "plus<PERSON>us", "178": "uni00B2", "179": "uni00B3", "180": "acute", "181": "uni00B5", "182": "paragraph", "183": "periodcentered", "184": "cedilla", "185": "uni00B9", "186": "ordmasculine", "187": "guil<PERSON><PERSON><PERSON>", "188": "onequarter", "189": "onehalf", "190": "threequarters", "191": "questiondown", "192": "<PERSON><PERSON>", "193": "Aacute", "194": "Acircumflex", "195": "<PERSON><PERSON>", "196": "Adieresis", "197": "<PERSON><PERSON>", "198": "AE", "199": "Ccedilla", "200": "<PERSON><PERSON>", "201": "Eacute", "202": "Ecircumflex", "203": "Edieresis", "204": "<PERSON><PERSON>", "205": "Iacute", "206": "Icircumflex", "207": "Idieresis", "208": "Eth", "209": "Ntilde", "210": "<PERSON><PERSON>", "211": "Oacute", "212": "Ocircumflex", "213": "<PERSON><PERSON><PERSON>", "214": "Odieresis", "215": "multiply", "216": "<PERSON><PERSON><PERSON>", "217": "<PERSON><PERSON>", "218": "Uacute", "219": "Ucircumflex", "220": "Udieresis", "221": "Ya<PERSON>", "222": "Thorn", "223": "germandbls", "224": "agrave", "225": "aacute", "226": "acircumflex", "227": "atilde", "228": "adieresis", "229": "aring", "230": "ae", "231": "ccedilla", "232": "egrave", "233": "eacute", "234": "ecircumflex", "235": "edieresis", "236": "igrave", "237": "iacute", "238": "icircumflex", "239": "idieresis", "240": "eth", "241": "ntilde", "242": "ograve", "243": "oacute", "244": "ocircumflex", "245": "otilde", "246": "odieresis", "247": "divide", "248": "oslash", "249": "ugrave", "250": "uacute", "251": "ucircumflex", "252": "udieresis", "253": "yacute", "254": "thorn", "255": "ydieresis", "256": "Amacron", "257": "amacron", "258": "Abreve", "259": "abreve", "260": "Aogonek", "261": "aogonek", "262": "Cacute", "263": "cacute", "264": "Ccircumflex", "265": "ccircumflex", "266": "Cdotaccent", "267": "cdotaccent", "268": "<PERSON><PERSON><PERSON>", "269": "ccaron", "270": "<PERSON><PERSON><PERSON>", "271": "dcaron", "272": "Dcroat", "273": "dmacron", "274": "Emacron", "275": "emacron", "276": "Ebreve", "277": "ebreve", "278": "Edotaccent", "279": "edotaccent", "280": "Eogonek", "281": "eogonek", "282": "<PERSON><PERSON><PERSON>", "283": "ecaron", "284": "Gcircumflex", "285": "gcircumflex", "286": "Gbreve", "287": "gbreve", "288": "Gdotaccent", "289": "gdotaccent", "290": "uni0122", "291": "uni0123", "292": "Hcircumflex", "293": "hcircumflex", "294": "H<PERSON>", "295": "hbar", "296": "Itilde", "297": "itilde", "298": "Imacron", "299": "imacron", "300": "Ibreve", "301": "i<PERSON>ve", "302": "Iogonek", "303": "iogonek", "304": "Idot", "305": "dotlessi", "306": "IJ", "307": "ij", "308": "Jcircumflex", "309": "jcircumflex", "310": "uni0136", "311": "uni0137", "312": "kgreenlandic", "313": "<PERSON><PERSON>", "314": "lacute", "315": "uni013B", "316": "uni013C", "317": "<PERSON><PERSON><PERSON>", "318": "lcaron", "319": "Ldot", "320": "ldot", "321": "Lslash", "322": "lslash", "323": "Nacute", "324": "nacute", "325": "uni0145", "326": "uni0146", "327": "<PERSON><PERSON><PERSON>", "328": "ncaron", "329": "napostrophe", "330": "Eng", "331": "eng", "332": "Omacron", "333": "omacron", "334": "Obreve", "335": "obreve", "336": "Ohungarumlau<PERSON>", "337": "ohungarumlaut", "338": "OE", "339": "oe", "340": "<PERSON><PERSON>", "341": "racute", "342": "uni0156", "343": "uni0157", "344": "<PERSON><PERSON><PERSON>", "345": "rcaron", "346": "Sacute", "347": "sacute", "348": "Scircumflex", "349": "scircumflex", "350": "Scedilla", "351": "scedilla", "352": "<PERSON><PERSON><PERSON>", "353": "scaron", "354": "uni0162", "355": "uni0163", "356": "<PERSON><PERSON><PERSON>", "357": "tcaron", "358": "Tbar", "359": "tbar", "360": "Utilde", "361": "utilde", "362": "Umacron", "363": "umacron", "364": "Ubreve", "365": "ubreve", "366": "<PERSON><PERSON>", "367": "uring", "368": "Uhungaru<PERSON>lau<PERSON>", "369": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "370": "Uogonek", "371": "uogonek", "372": "Wcircumflex", "373": "wcircumflex", "374": "Ycircumflex", "375": "ycircumflex", "376": "Ydieresis", "377": "Zacute", "378": "zacute", "379": "Zdotaccent", "380": "zdotaccent", "381": "<PERSON><PERSON><PERSON>", "382": "z<PERSON>on", "383": "longs", "399": "uni018F", "402": "florin", "416": "<PERSON><PERSON>", "417": "ohorn", "431": "<PERSON><PERSON>", "432": "uhorn", "496": "uni01F0", "506": "Aringacute", "507": "aring<PERSON><PERSON>", "508": "AEacute", "509": "aeacute", "510": "Oslashacute", "511": "oslashacute", "536": "uni0218", "537": "uni0219", "538": "uni021A", "539": "uni021B", "567": "uni0237", "601": "uni0259", "700": "uni02BC", "710": "circumflex", "711": "caron", "713": "uni02C9", "728": "breve", "729": "dotaccent", "730": "ring", "731": "ogonek", "732": "tilde", "733": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "755": "uni02F3", "768": "gravecomb", "769": "acutecomb", "770": "uni0302", "771": "tildecomb", "772": "uni0304", "774": "uni0306", "775": "uni0307", "776": "uni0308", "777": "hookab<PERSON><PERSON>", "778": "uni030A", "779": "uni030B", "780": "uni030C", "783": "uni030F", "786": "uni0312", "795": "uni031B", "803": "dotbelowcomb", "805": "uni0325", "806": "uni0326", "807": "uni0327", "808": "uni0328", "821": "uni0335", "822": "uni0336", "823": "uni0337", "824": "uni0338", "900": "tonos", "901": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "902": "Alphatonos", "903": "anoteleia", "904": "Epsilontonos", "905": "Etatonos", "906": "Iotatonos", "908": "Omicrontonos", "910": "Upsilontonos", "911": "Omegatonos", "912": "iotadieresistonos", "913": "Alpha", "914": "Beta", "915": "Gamma", "916": "uni0394", "917": "Epsilon", "918": "Zeta", "919": "Eta", "920": "Theta", "921": "Iota", "922": "Kappa", "923": "Lambda", "924": "Mu", "925": "<PERSON>u", "926": "Xi", "927": "Omicron", "928": "Pi", "929": "Rho", "931": "Sigma", "932": "Tau", "933": "Upsilon", "934": "Phi", "935": "<PERSON>", "936": "Psi", "937": "uni03A9", "938": "Iotadieresis", "939": "Upsilondieresis", "940": "alphatonos", "941": "epsilontonos", "942": "etatonos", "943": "iotatonos", "944": "upsilondieresistonos", "945": "alpha", "946": "beta", "947": "gamma", "948": "delta", "949": "epsilon", "950": "zeta", "951": "eta", "952": "theta", "953": "iota", "954": "kappa", "955": "lambda", "956": "uni03BC", "957": "nu", "958": "xi", "959": "omicron", "960": "pi", "961": "rho", "962": "uni03C2", "963": "sigma", "964": "tau", "965": "upsilon", "966": "phi", "967": "chi", "968": "psi", "969": "omega", "970": "iotadieresis", "971": "upsilondieresis", "972": "omicrontonos", "973": "upsilontonos", "974": "omegatonos", "977": "uni03D1", "978": "uni03D2", "982": "uni03D6", "1024": "uni0400", "1025": "uni0401", "1026": "uni0402", "1027": "uni0403", "1028": "uni0404", "1029": "uni0405", "1030": "uni0406", "1031": "uni0407", "1032": "uni0408", "1033": "uni0409", "1034": "uni040A", "1035": "uni040B", "1036": "uni040C", "1037": "uni040D", "1038": "uni040E", "1039": "uni040F", "1040": "uni0410", "1041": "uni0411", "1042": "uni0412", "1043": "uni0413", "1044": "uni0414", "1045": "uni0415", "1046": "uni0416", "1047": "uni0417", "1048": "uni0418", "1049": "uni0419", "1050": "uni041A", "1051": "uni041B", "1052": "uni041C", "1053": "uni041D", "1054": "uni041E", "1055": "uni041F", "1056": "uni0420", "1057": "uni0421", "1058": "uni0422", "1059": "uni0423", "1060": "uni0424", "1061": "uni0425", "1062": "uni0426", "1063": "uni0427", "1064": "uni0428", "1065": "uni0429", "1066": "uni042A", "1067": "uni042B", "1068": "uni042C", "1069": "uni042D", "1070": "uni042E", "1071": "uni042F", "1072": "uni0430", "1073": "uni0431", "1074": "uni0432", "1075": "uni0433", "1076": "uni0434", "1077": "uni0435", "1078": "uni0436", "1079": "uni0437", "1080": "uni0438", "1081": "uni0439", "1082": "uni043A", "1083": "uni043B", "1084": "uni043C", "1085": "uni043D", "1086": "uni043E", "1087": "uni043F", "1088": "uni0440", "1089": "uni0441", "1090": "uni0442", "1091": "uni0443", "1092": "uni0444", "1093": "uni0445", "1094": "uni0446", "1095": "uni0447", "1096": "uni0448", "1097": "uni0449", "1098": "uni044A", "1099": "uni044B", "1100": "uni044C", "1101": "uni044D", "1102": "uni044E", "1103": "uni044F", "1104": "uni0450", "1105": "uni0451", "1106": "uni0452", "1107": "uni0453", "1108": "uni0454", "1109": "uni0455", "1110": "uni0456", "1111": "uni0457", "1112": "uni0458", "1113": "uni0459", "1114": "uni045A", "1115": "uni045B", "1116": "uni045C", "1117": "uni045D", "1118": "uni045E", "1119": "uni045F", "1120": "uni0460", "1121": "uni0461", "1122": "uni0462", "1123": "uni0463", "1124": "uni0464", "1125": "uni0465", "1126": "uni0466", "1127": "uni0467", "1128": "uni0468", "1129": "uni0469", "1130": "uni046A", "1131": "uni046B", "1132": "uni046C", "1133": "uni046D", "1134": "uni046E", "1135": "uni046F", "1136": "uni0470", "1137": "uni0471", "1138": "uni0472", "1139": "uni0473", "1140": "uni0474", "1141": "uni0475", "1142": "uni0476", "1143": "uni0477", "1144": "uni0478", "1145": "uni0479", "1146": "uni047A", "1147": "uni047B", "1148": "uni047C", "1149": "uni047D", "1150": "uni047E", "1151": "uni047F", "1152": "uni0480", "1153": "uni0481", "1154": "uni0482", "1155": "uni0483", "1156": "uni0484", "1157": "uni0485", "1158": "uni0486", "1160": "uni0488", "1161": "uni0489", "1162": "uni048A", "1163": "uni048B", "1164": "uni048C", "1165": "uni048D", "1166": "uni048E", "1167": "uni048F", "1168": "uni0490", "1169": "uni0491", "1170": "uni0492", "1171": "uni0493", "1172": "uni0494", "1173": "uni0495", "1174": "uni0496", "1175": "uni0497", "1176": "uni0498", "1177": "uni0499", "1178": "uni049A", "1179": "uni049B", "1180": "uni049C", "1181": "uni049D", "1182": "uni049E", "1183": "uni049F", "1184": "uni04A0", "1185": "uni04A1", "1186": "uni04A2", "1187": "uni04A3", "1188": "uni04A4", "1189": "uni04A5", "1190": "uni04A6", "1191": "uni04A7", "1192": "uni04A8", "1193": "uni04A9", "1194": "uni04AA", "1195": "uni04AB", "1196": "uni04AC", "1197": "uni04AD", "1198": "Ustraitcy", "1199": "ustraitcy", "1200": "Ustraitstrokecy", "1201": "ustraitstrokecy", "1202": "uni04B2", "1203": "uni04B3", "1204": "uni04B4", "1205": "uni04B5", "1206": "uni04B6", "1207": "uni04B7", "1208": "uni04B8", "1209": "uni04B9", "1210": "uni04BA", "1211": "uni04BB", "1212": "uni04BC", "1213": "uni04BD", "1214": "uni04BE", "1215": "uni04BF", "1216": "uni04C0", "1217": "uni04C1", "1218": "uni04C2", "1219": "uni04C3", "1220": "uni04C4", "1221": "uni04C5", "1222": "uni04C6", "1223": "uni04C7", "1224": "uni04C8", "1225": "uni04C9", "1226": "uni04CA", "1227": "uni04CB", "1228": "uni04CC", "1229": "uni04CD", "1230": "uni04CE", "1231": "uni04CF", "1232": "uni04D0", "1233": "uni04D1", "1234": "uni04D2", "1235": "uni04D3", "1236": "uni04D4", "1237": "uni04D5", "1238": "uni04D6", "1239": "uni04D7", "1240": "uni04D8", "1241": "uni04D9", "1242": "uni04DA", "1243": "uni04DB", "1244": "uni04DC", "1245": "uni04DD", "1246": "uni04DE", "1247": "uni04DF", "1248": "uni04E0", "1249": "uni04E1", "1250": "uni04E2", "1251": "uni04E3", "1252": "uni04E4", "1253": "uni04E5", "1254": "uni04E6", "1255": "uni04E7", "1256": "uni04E8", "1257": "uni04E9", "1258": "uni04EA", "1259": "uni04EB", "1260": "uni04EC", "1261": "uni04ED", "1262": "uni04EE", "1263": "uni04EF", "1264": "uni04F0", "1265": "uni04F1", "1266": "uni04F2", "1267": "uni04F3", "1268": "uni04F4", "1269": "uni04F5", "1270": "uni04F6", "1271": "uni04F7", "1272": "uni04F8", "1273": "uni04F9", "1274": "uni04FA", "1275": "uni04FB", "1276": "uni04FC", "1277": "uni04FD", "1278": "uni04FE", "1279": "uni04FF", "1280": "uni0500", "1281": "uni0501", "1282": "uni0502", "1283": "uni0503", "1284": "uni0504", "1285": "uni0505", "1286": "uni0506", "1287": "uni0507", "1288": "uni0508", "1289": "uni0509", "1290": "uni050A", "1291": "uni050B", "1292": "uni050C", "1293": "uni050D", "1294": "uni050E", "1295": "uni050F", "1296": "uni0510", "1297": "uni0511", "1298": "uni0512", "1299": "uni0513", "7680": "uni1E00", "7681": "uni1E01", "7742": "uni1E3E", "7743": "uni1E3F", "7808": "<PERSON><PERSON>", "7809": "wgrave", "7810": "Wacute", "7811": "wacute", "7812": "Wdieresis", "7813": "wdieresis", "7838": "uni1E9E", "7840": "uni1EA0", "7841": "uni1EA1", "7842": "uni1EA2", "7843": "uni1EA3", "7844": "uni1EA4", "7845": "uni1EA5", "7846": "uni1EA6", "7847": "uni1EA7", "7848": "uni1EA8", "7849": "uni1EA9", "7850": "uni1EAA", "7851": "uni1EAB", "7852": "uni1EAC", "7853": "uni1EAD", "7854": "uni1EAE", "7855": "uni1EAF", "7856": "uni1EB0", "7857": "uni1EB1", "7858": "uni1EB2", "7859": "uni1EB3", "7860": "uni1EB4", "7861": "uni1EB5", "7862": "uni1EB6", "7863": "uni1EB7", "7864": "uni1EB8", "7865": "uni1EB9", "7866": "uni1EBA", "7867": "uni1EBB", "7868": "uni1EBC", "7869": "uni1EBD", "7870": "uni1EBE", "7871": "uni1EBF", "7872": "uni1EC0", "7873": "uni1EC1", "7874": "uni1EC2", "7875": "uni1EC3", "7876": "uni1EC4", "7877": "uni1EC5", "7878": "uni1EC6", "7879": "uni1EC7", "7880": "uni1EC8", "7881": "uni1EC9", "7882": "uni1ECA", "7883": "uni1ECB", "7884": "uni1ECC", "7885": "uni1ECD", "7886": "uni1ECE", "7887": "uni1ECF", "7888": "uni1ED0", "7889": "uni1ED1", "7890": "uni1ED2", "7891": "uni1ED3", "7892": "uni1ED4", "7893": "uni1ED5", "7894": "uni1ED6", "7895": "uni1ED7", "7896": "uni1ED8", "7897": "uni1ED9", "7898": "uni1EDA", "7899": "uni1EDB", "7900": "uni1EDC", "7901": "uni1EDD", "7902": "uni1EDE", "7903": "uni1EDF", "7904": "uni1EE0", "7905": "uni1EE1", "7906": "uni1EE2", "7907": "uni1EE3", "7908": "uni1EE4", "7909": "uni1EE5", "7910": "uni1EE6", "7911": "uni1EE7", "7912": "uni1EE8", "7913": "uni1EE9", "7914": "uni1EEA", "7915": "uni1EEB", "7916": "uni1EEC", "7917": "uni1EED", "7918": "uni1EEE", "7919": "uni1EEF", "7920": "uni1EF0", "7921": "uni1EF1", "7922": "<PERSON><PERSON>", "7923": "ygrave", "7924": "uni1EF4", "7925": "uni1EF5", "7926": "uni1EF6", "7927": "uni1EF7", "7928": "uni1EF8", "7929": "uni1EF9", "8013": "uni1F4D", "8189": "uni1FFD", "8190": "uni1FFE", "8192": "uni2000", "8193": "uni2001", "8194": "uni2002", "8195": "uni2003", "8196": "uni2004", "8197": "uni2005", "8198": "uni2006", "8199": "uni2007", "8200": "uni2008", "8201": "uni2009", "8202": "uni200A", "8203": "uni200B", "8211": "endash", "8212": "emdash", "8213": "uni2015", "8215": "underscoredbl", "8216": "quoteleft", "8217": "quoteright", "8218": "quotesinglbase", "8219": "quotereversed", "8220": "quotedblleft", "8221": "<PERSON><PERSON><PERSON><PERSON>", "8222": "quotedblbase", "8224": "dagger", "8225": "daggerdbl", "8226": "bullet", "8229": "twodotenleader", "8230": "ellipsis", "8240": "perth<PERSON>and", "8242": "minute", "8243": "second", "8249": "guil<PERSON>lle<PERSON>", "8250": "guil<PERSON><PERSON><PERSON>", "8252": "exclamdbl", "8260": "fraction", "8308": "uni2074", "8319": "uni207F", "8355": "franc", "8356": "lira", "8359": "peseta", "8363": "dong", "8364": "Euro", "8453": "uni2105", "8467": "uni2113", "8470": "uni2116", "8482": "trademark", "8486": "uni03A9", "8494": "estimated", "8539": "oneeighth", "8540": "threeeighths", "8541": "fiveeighths", "8542": "seveneighths", "8706": "<PERSON><PERSON><PERSON>", "8710": "uni0394", "8719": "product", "8721": "summation", "8722": "minus", "8725": "uni2215", "8730": "radical", "8734": "infinity", "8747": "integral", "8776": "approxequal", "8800": "notequal", "8804": "lessequal", "8805": "greaterequal", "9674": "lozenge", "63171": "uni0326.1", "64257": "fi", "64258": "fl", "64259": "uniFB03", "64260": "uniFB04", "65279": "uniFEFF", "65532": "uniFFFC", "65533": "uniFFFD"}, "isUnicode": true, "EncodingScheme": "FontSpecific", "FontName": "Roboto Slab SemiBold", "FullName": "Roboto Slab SemiBold", "Version": "Version 2.002", "PostScriptName": "RobotoSlab-SemiBold", "Weight": "Bold", "ItalicAngle": "0", "IsFixedPitch": "false", "UnderlineThickness": "49", "UnderlinePosition": "-73", "FontHeightOffset": "0", "Ascender": "1048", "Descender": "-271", "FontBBox": ["-181", "-271", "1305", "1048"], "StartCharMetrics": "1154", "C": {"13": 246, "32": 246, "33": 245, "34": 382, "35": 590, "36": 541, "37": 701, "38": 625, "39": 220, "40": 332, "41": 342, "42": 451, "43": 541, "44": 235, "45": 396, "46": 252, "47": 392, "48": 571, "49": 436, "50": 554, "51": 542, "52": 568, "53": 532, "54": 559, "55": 542, "56": 546, "57": 559, "58": 218, "59": 218, "60": 495, "61": 549, "62": 506, "63": 478, "64": 883, "65": 740, "66": 654, "67": 642, "68": 689, "69": 638, "70": 617, "71": 667, "72": 779, "73": 346, "74": 583, "75": 750, "76": 587, "77": 1000, "78": 782, "79": 696, "80": 644, "81": 700, "82": 685, "83": 604, "84": 680, "85": 756, "86": 747, "87": 1067, "88": 741, "89": 731, "90": 596, "91": 289, "92": 416, "93": 280, "94": 430, "95": 542, "96": 278, "97": 561, "98": 573, "99": 527, "100": 592, "101": 524, "102": 382, "103": 581, "104": 637, "105": 322, "106": 285, "107": 640, "108": 319, "109": 942, "110": 642, "111": 561, "112": 601, "113": 563, "114": 429, "115": 503, "116": 358, "117": 619, "118": 604, "119": 890, "120": 632, "121": 618, "122": 533, "123": 332, "124": 211, "125": 333, "126": 652, "160": 246, "161": 245, "162": 536, "163": 575, "164": 702, "165": 683, "166": 249, "167": 606, "168": 386, "169": 768, "170": 426, "171": 463, "172": 539, "174": 768, "175": 395, "176": 365, "177": 509, "178": 402, "179": 408, "180": 278, "181": 654, "182": 546, "183": 266, "184": 241, "185": 263, "186": 449, "187": 472, "188": 693, "189": 714, "190": 804, "191": 456, "192": 740, "193": 740, "194": 740, "195": 740, "196": 740, "197": 740, "198": 1022, "199": 642, "200": 638, "201": 638, "202": 638, "203": 638, "204": 346, "205": 346, "206": 346, "207": 346, "208": 689, "209": 782, "210": 696, "211": 696, "212": 696, "213": 696, "214": 696, "215": 522, "216": 696, "217": 756, "218": 756, "219": 756, "220": 756, "221": 731, "222": 629, "223": 642, "224": 561, "225": 561, "226": 561, "227": 561, "228": 561, "229": 561, "230": 842, "231": 527, "232": 524, "233": 524, "234": 524, "235": 524, "236": 322, "237": 322, "238": 322, "239": 322, "240": 591, "241": 642, "242": 561, "243": 561, "244": 561, "245": 561, "246": 561, "247": 555, "248": 561, "249": 619, "250": 619, "251": 619, "252": 619, "253": 618, "254": 598, "255": 618, "256": 740, "257": 561, "258": 740, "259": 561, "260": 740, "261": 561, "262": 642, "263": 527, "264": 642, "265": 527, "266": 642, "267": 527, "268": 642, "269": 527, "270": 689, "271": 593, "272": 689, "273": 592, "274": 638, "275": 524, "276": 638, "277": 524, "278": 638, "279": 524, "280": 638, "281": 524, "282": 638, "283": 524, "284": 667, "285": 581, "286": 667, "287": 581, "288": 667, "289": 581, "290": 667, "291": 581, "292": 779, "293": 637, "294": 779, "295": 637, "296": 346, "297": 322, "298": 346, "299": 322, "300": 346, "301": 322, "302": 346, "303": 322, "304": 346, "305": 322, "306": 924, "307": 613, "308": 583, "309": 285, "310": 750, "311": 640, "312": 632, "313": 587, "314": 319, "315": 587, "316": 319, "317": 587, "318": 319, "319": 587, "320": 319, "321": 587, "322": 319, "323": 782, "324": 642, "325": 782, "326": 642, "327": 782, "328": 642, "329": 642, "330": 771, "331": 624, "332": 696, "333": 561, "334": 696, "335": 561, "336": 696, "337": 561, "338": 986, "339": 894, "340": 685, "341": 429, "342": 685, "343": 429, "344": 685, "345": 429, "346": 604, "347": 503, "348": 604, "349": 503, "350": 604, "351": 503, "352": 604, "353": 503, "354": 680, "355": 358, "356": 680, "357": 358, "358": 680, "359": 358, "360": 756, "361": 619, "362": 756, "363": 619, "364": 756, "365": 619, "366": 756, "367": 619, "368": 756, "369": 619, "370": 756, "371": 619, "372": 1067, "373": 890, "374": 731, "375": 618, "376": 731, "377": 596, "378": 533, "379": 596, "380": 533, "381": 596, "382": 533, "383": 366, "399": 684, "402": 386, "416": 696, "417": 583, "431": 861, "432": 673, "496": 285, "506": 740, "507": 561, "508": 1022, "509": 842, "510": 696, "511": 561, "536": 604, "537": 503, "538": 680, "539": 358, "567": 285, "601": 546, "700": 225, "710": 381, "711": 381, "713": 395, "728": 370, "729": 177, "730": 261, "731": 230, "732": 399, "733": 443, "755": 256, "768": 0, "769": 0, "770": 0, "771": 0, "772": 0, "774": 0, "775": 0, "776": 0, "777": 0, "778": 0, "779": 0, "780": 0, "783": 0, "786": 0, "795": 0, "803": 0, "805": 0, "806": 0, "807": 0, "808": 0, "821": 0, "822": 0, "823": 0, "824": 0, "900": 249, "901": 415, "902": 740, "903": 266, "904": 757, "905": 905, "906": 473, "908": 709, "910": 858, "911": 689, "912": 340, "913": 740, "914": 654, "915": 596, "916": 710, "917": 638, "918": 596, "919": 779, "920": 710, "921": 349, "922": 750, "923": 715, "924": 1000, "925": 782, "926": 601, "927": 696, "928": 779, "929": 644, "931": 576, "932": 680, "933": 731, "934": 732, "935": 699, "936": 839, "937": 658, "938": 349, "939": 731, "940": 592, "941": 540, "942": 595, "943": 340, "944": 590, "945": 592, "946": 583, "947": 541, "948": 565, "949": 540, "950": 505, "951": 595, "952": 575, "953": 340, "954": 632, "955": 550, "956": 654, "957": 567, "958": 522, "959": 561, "960": 635, "961": 572, "962": 525, "963": 606, "964": 485, "965": 590, "966": 751, "967": 582, "968": 802, "969": 826, "970": 340, "971": 590, "972": 561, "973": 590, "974": 826, "977": 671, "978": 619, "982": 867, "1024": 638, "1025": 638, "1026": 779, "1027": 596, "1028": 633, "1029": 604, "1030": 343, "1031": 346, "1032": 581, "1033": 1060, "1034": 1094, "1035": 851, "1036": 713, "1037": 780, "1038": 682, "1039": 779, "1040": 740, "1041": 667, "1042": 660, "1043": 596, "1044": 769, "1045": 638, "1046": 1040, "1047": 596, "1048": 780, "1049": 780, "1050": 713, "1051": 744, "1052": 1000, "1053": 779, "1054": 707, "1055": 779, "1056": 645, "1057": 649, "1058": 680, "1059": 682, "1060": 798, "1061": 741, "1062": 794, "1063": 755, "1064": 1024, "1065": 1032, "1066": 872, "1067": 954, "1068": 664, "1069": 618, "1070": 968, "1071": 673, "1072": 561, "1073": 572, "1074": 625, "1075": 530, "1076": 654, "1077": 524, "1078": 891, "1079": 517, "1080": 690, "1081": 690, "1082": 643, "1083": 649, "1084": 857, "1085": 689, "1086": 561, "1087": 690, "1088": 605, "1089": 527, "1090": 601, "1091": 618, "1092": 727, "1093": 632, "1094": 675, "1095": 669, "1096": 935, "1097": 941, "1098": 716, "1099": 882, "1100": 608, "1101": 527, "1102": 856, "1103": 640, "1104": 524, "1105": 524, "1106": 636, "1107": 530, "1108": 542, "1109": 503, "1110": 324, "1111": 322, "1112": 290, "1113": 878, "1114": 927, "1115": 637, "1116": 643, "1117": 690, "1118": 618, "1119": 690, "1120": 907, "1121": 824, "1122": 664, "1123": 603, "1124": 922, "1125": 803, "1126": 722, "1127": 605, "1128": 1008, "1129": 906, "1130": 939, "1131": 805, "1132": 1234, "1133": 1101, "1134": 544, "1135": 483, "1136": 839, "1137": 802, "1138": 684, "1139": 581, "1140": 686, "1141": 578, "1142": 686, "1143": 578, "1144": 1314, "1145": 1163, "1146": 707, "1147": 564, "1148": 888, "1149": 765, "1150": 907, "1151": 816, "1152": 611, "1153": 519, "1154": 541, "1155": 0, "1156": 0, "1157": 0, "1158": 0, "1160": 984, "1161": 945, "1162": 780, "1163": 690, "1164": 664, "1165": 599, "1166": 670, "1167": 605, "1168": 594, "1169": 529, "1170": 596, "1171": 530, "1172": 638, "1173": 572, "1174": 1040, "1175": 891, "1176": 596, "1177": 517, "1178": 713, "1179": 643, "1180": 746, "1181": 693, "1182": 713, "1183": 619, "1184": 921, "1185": 807, "1186": 779, "1187": 689, "1188": 1004, "1189": 777, "1190": 1054, "1191": 926, "1192": 777, "1193": 651, "1194": 649, "1195": 527, "1196": 680, "1197": 601, "1198": 731, "1199": 541, "1200": 731, "1201": 541, "1202": 741, "1203": 632, "1204": 919, "1205": 821, "1206": 755, "1207": 669, "1208": 756, "1209": 669, "1210": 763, "1211": 637, "1212": 765, "1213": 659, "1214": 765, "1215": 659, "1216": 343, "1217": 1040, "1218": 891, "1219": 709, "1220": 605, "1221": 744, "1222": 649, "1223": 771, "1224": 680, "1225": 779, "1226": 689, "1227": 755, "1228": 669, "1229": 1000, "1230": 857, "1231": 343, "1232": 740, "1233": 561, "1234": 740, "1235": 561, "1236": 1022, "1237": 842, "1238": 638, "1239": 524, "1240": 684, "1241": 546, "1242": 684, "1243": 546, "1244": 1040, "1245": 891, "1246": 596, "1247": 517, "1248": 562, "1249": 563, "1250": 780, "1251": 690, "1252": 780, "1253": 690, "1254": 707, "1255": 561, "1256": 684, "1257": 581, "1258": 684, "1259": 581, "1260": 618, "1261": 527, "1262": 682, "1263": 618, "1264": 682, "1265": 618, "1266": 682, "1267": 618, "1268": 755, "1269": 669, "1270": 596, "1271": 530, "1272": 954, "1273": 882, "1274": 596, "1275": 530, "1276": 741, "1277": 632, "1278": 741, "1279": 632, "1280": 662, "1281": 593, "1282": 846, "1283": 841, "1284": 789, "1285": 708, "1286": 582, "1287": 553, "1288": 964, "1289": 843, "1290": 1015, "1291": 902, "1292": 626, "1293": 530, "1294": 728, "1295": 660, "1296": 629, "1297": 540, "1298": 744, "1299": 649, "7680": 740, "7681": 561, "7742": 1000, "7743": 942, "7808": 1067, "7809": 890, "7810": 1067, "7811": 890, "7812": 1067, "7813": 890, "7838": 715, "7840": 740, "7841": 561, "7842": 740, "7843": 561, "7844": 740, "7845": 561, "7846": 740, "7847": 561, "7848": 740, "7849": 561, "7850": 740, "7851": 561, "7852": 740, "7853": 561, "7854": 740, "7855": 561, "7856": 740, "7857": 561, "7858": 740, "7859": 561, "7860": 740, "7861": 561, "7862": 740, "7863": 561, "7864": 638, "7865": 524, "7866": 638, "7867": 524, "7868": 638, "7869": 524, "7870": 638, "7871": 524, "7872": 638, "7873": 524, "7874": 638, "7875": 524, "7876": 638, "7877": 524, "7878": 638, "7879": 524, "7880": 346, "7881": 322, "7882": 346, "7883": 322, "7884": 696, "7885": 561, "7886": 696, "7887": 561, "7888": 696, "7889": 561, "7890": 696, "7891": 561, "7892": 696, "7893": 561, "7894": 696, "7895": 561, "7896": 696, "7897": 561, "7898": 696, "7899": 583, "7900": 696, "7901": 583, "7902": 696, "7903": 583, "7904": 696, "7905": 583, "7906": 696, "7907": 583, "7908": 756, "7909": 619, "7910": 756, "7911": 619, "7912": 861, "7913": 673, "7914": 861, "7915": 673, "7916": 861, "7917": 673, "7918": 861, "7919": 673, "7920": 861, "7921": 673, "7922": 731, "7923": 618, "7924": 731, "7925": 618, "7926": 731, "7927": 618, "7928": 731, "7929": 618, "8013": 696, "8189": 249, "8190": 185, "8192": 262, "8193": 523, "8194": 262, "8195": 523, "8196": 175, "8197": 131, "8198": 87, "8199": 288, "8200": 140, "8201": 104, "8202": 52, "8203": 0, "8211": 673, "8212": 781, "8213": 781, "8215": 547, "8216": 228, "8217": 225, "8218": 235, "8219": 253, "8220": 397, "8221": 401, "8222": 401, "8224": 539, "8225": 555, "8226": 342, "8229": 483, "8230": 702, "8240": 942, "8242": 220, "8243": 380, "8249": 281, "8250": 283, "8252": 490, "8260": 432, "8308": 447, "8319": 461, "8355": 617, "8356": 587, "8359": 863, "8363": 608, "8364": 518, "8453": 698, "8467": 477, "8470": 1172, "8482": 606, "8486": 658, "8494": 572, "8539": 800, "8540": 869, "8541": 897, "8542": 816, "8706": 590, "8710": 710, "8719": 742, "8721": 615, "8722": 549, "8725": 432, "8730": 572, "8734": 1031, "8747": 344, "8776": 584, "8800": 532, "8804": 501, "8805": 509, "9674": 518, "63171": 0, "64257": 643, "64258": 687, "64259": 1011, "64260": 1069, "65279": 0, "65532": 1074, "65533": 950}, "CIDtoGID_Compressed": true, "CIDtoGID": "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", "_version_": 6}
<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>Menu</title>
    <style>
        @font-face {
            font-family: 'R';
            src: url({{ storage_path("fonts/RobotoSlab-Regular.ttf") }}) format("truetype");
            font-weight: 500;
            font-style: normal;
        }

        @font-face {
            font-family: 'R';
            src: url({{ storage_path("fonts/RobotoSlab-Bold.ttf") }}) format("truetype");
            font-weight: 700;
            font-style: normal;
        }

        @page {
            margin: 42mm 24mm 20mm 24mm;
        }

        .page {
            width: 210mm; /* A4 width */
            height: 297mm; /* A4 height */

            box-sizing: border-box;
        }

        body {
            font-family: R, serif;
            font-weight: 500;
            margin: 0;
            padding: 0;
            font-size: 14px;
            line-height: 15px;
        }


        td {
            vertical-align: top;
        }

        .page_break {
            page-break-before: always;
        }

        .category-title {
            font-weight: bold;
            font-size: 20px;
        }

        .food-number {
            width: 25mm;
            font-size: 12px;
        }

        .food-name {
            font-weight: 700;

        }

        .food-price {
            width: 20mm;
        }

        .food-box {
            width: 130mm;
            padding-bottom: 2mm;
        }

        .food-description {
            font-size: 13px;
        }

        .category {
            page-break-inside: avoid;
        }
    </style>
</head>
<body>
<div class="page ">
    @foreach($categories as $category)
        @if($category->break)
            <div class="page_break"></div>
        @endif
        <div class="category">
            <table>
                <tr>
                    <td class="food-number"></td>
                    <td>
                        <div class="category-title">{{ $category->name }}</div>
                    </td>
                </tr>
            </table>
            @foreach($category->items as $food)
                <table>
                    <tr>
                        <td class="food-number">{{ $food->number }}.</td>
                        <td class="food-box">
                            <div class="food-name">{{ $food->name }}</div>
                            <div class="food-description">{!! nl2br($food->description) !!}</div>
                        </td>
                        <td class="food-price">{{ $food->price }}</td>
                    </tr>
                </table>
            @endforeach
        </div>
        @if($category->break)
            <div class="page_break"></div>
        @endif
    @endforeach
    <table>
        <tr>
            <td class="food-number"></td>
            <td class="food-box">
                <p>Kolektiv restaurace Vám přeje příjemné posezení a dobrou chuť.<br/> Při objednání poloviční porce
                    účtujeme 75% ceny jídel.<br/> Ceny jsou účtovány včetně DPH.
                </p>
            </td>
        </tr>
    </table>
</div>
</body>
</html>

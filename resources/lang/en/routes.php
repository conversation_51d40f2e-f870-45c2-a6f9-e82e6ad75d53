<?php

return [
    'about' => 'about-us',
    'career' => 'career',
    'news' => 'news',
    'news/blog' => 'news/blog',
    'news/blog/{slug}' => 'news/blog/{slug}',
    'contacts' => 'contact',
    'portfolio' => 'portfolio',
    'people' => 'team',
    'code-of-conduct' => 'code-of-conduct',
    'anti-bribery-and-anti-corruption-policy' => 'anti-bribery-and-anti-corruption-policy',
];

//
//Route::multilingual('/o-nas', [AboutController::class, 'index'])->name('about');
//Route::multilingual('/lide', [PeopleController::class, 'index'])->name('people');
//Route::multilingual('/portfolio', [PortfolioController::class, 'index'])->name('portfolio');
////Route::multilingual('/press-room', [NewsController::class, 'index'])->name('pressroom');
//Route::multilingual('/kariera', [CareerController::class, 'index'])->name('career');
//Route::multilingual('/kontakty', [ContactsController::class, 'index'])->name('contacts');

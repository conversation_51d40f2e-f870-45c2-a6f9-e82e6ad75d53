@extends('front-end.layouts.app')

@section('title', __('navigation.portfolio'))

@section('styles')
    <style>
        /* Additional inline styles if needed */
    </style>
@endsection

@section('content')
    {{--<div class="uk-section-default people-background uk-inverse- uk-section uk-section-large uk-padding-remove-bottom portfolio-page"--}}
    {{--     uk-scrollspy="target: [uk-scrollspy-class]; cls: uk-animation-fade; delay: false;">--}}
    <div class="uk-margin-remove-top uk-section uk-padding-remove portfolio-page"
         uk-scrollspy="target: [uk-scrollspy-class]; cls: uk-animation-fade; delay: false;">

        @include('front-end.components.navbar', ['navMode' => 'white'])

        <div class="uk-section uk-section-large ">
            <div class="uk-container uk-container-large">
                <h2 style="color: rgba(189, 158, 87, 0.95);"
                    class="uk-heading-small uk-text-uppercase uk-text-center@s">
                    {{ $portfolioContent[$lang]['title'] }}
                </h2>
                <div class="uk-width-3-5@m uk-margin-auto">
                    <div class="uk-text-center@s" style="color: #fffafa;">
                        {!! $portfolioContent[$lang]['description'] !!}
                    </div>
                </div>


                <!-- Filters -->
                <div class="uk-margin-medium-bottom uk-flex uk-flex-center uk-margin-medium-top">

                    <form class="uk-grid-small uk-flex-middle uk-width-auto"
                          style=""
                          uk-grid>
                        <div class="uk-width-auto@m">
                            <label style="color: #fffafa"
                                   class="uk-form-label">{{ $lang == 'cs' ? 'Filtrovat podle:' : 'Filter by:' }}</label>
                        </div>
                        <div class="uk-width-1-3@m" style="min-width: 170px">
                            <select class="uk-select" name="industry" onchange="this.form.submit()">
                                <option value="all" {{ $industryFilter == 'all' ? 'selected' : '' }}>
                                    {{ $lang == 'cs' ? 'Všechny obory' : 'All industries' }}
                                </option>
                                @foreach($industries as $key => $industry)
                                    <option value="{{ $key }}" {{ $industryFilter == $key ? 'selected' : '' }}>
                                        {{ $industry[$lang] }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        <div class="uk-width-1-3@m" style="min-width: 170px">
                            <select class="uk-select" name="country" onchange="this.form.submit()">
                                <option value="all" {{ $countryFilter == 'all' ? 'selected' : '' }}>
                                    {{ $lang == 'cs' ? 'Všechny země' : 'All countries' }}
                                </option>
                                @foreach($countries as $country)
                                    <option value="{{ $country }}" {{ $countryFilter == $country ? 'selected' : '' }}>
                                        {{ $country }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                    </form>

                </div>

                <!-- Portfolio Companies Grid -->
                <div class="uk-child-width-1-2 uk-child-width-1-3@m uk-child-width-1-4@l uk-flex-center" uk-grid>
                    @foreach($filteredData as $company)
                        <div>
                            <div class="uk-card uk-card-default bhm-border-rounded portfolio-card">
                                <a href="#{{$company['id']}}-{{$loop->index}}" class="uk-link-reset" uk-toggle>
                                    <div class="portfolio-card-flag">
                                        <img
                                            src="https://hatscripts.github.io/circle-flags/flags/{{ $getCountryCode($company['country']) }}.svg"
                                            width="24" class="country-flag-icon" alt="{{ $company['country'] }}"
                                            title="{{ $company['country'] }}">
                                    </div>
                                    <div class="portfolio-card-logo-container">
                                        @if(strpos($company['logo'], '/') === 0)
                                            <img src="{{ $company['logo'] }}" alt="{{ $company['name'] }}"
                                                 class="portfolio-card-logo">
                                        @else
                                            <img src="{{ asset('images/projects/' . $company['logo']) }}"
                                                 alt="{{ $company['name'] }}" class="portfolio-card-logo">
                                        @endif
                                    </div>
                                    <div class="uk-text-center">
                                        <div class="portfolio-card-industry-tag industry-{{ $company['industry'] }}">
                                            <span>{{ $industries[$company['industry']][$lang] }}</span>
                                        </div>
                                    </div>
                                    <div class="portfolio-card-content">
                                        <h3 class="portfolio-card-title uk-margin-remove-bottom">{{ isset($company['name_'.$lang]) ? $company['name_'.$lang] : $company['name'] }}</h3>
                                    </div>
                                </a>
                            </div>
                            <div id="{{$company['id']}}-{{$loop->index}}" class="uk-modal-full" uk-modal>
                                <div class="uk-modal-dialog"
                                     style="background-image: url('/images/home/<USER>'); background-size: 5000px; background-position: center center;">
                                    <button class="uk-modal-close-full uk-close-large" type="button" uk-close></button>
                                    <div class="uk-grid-collapse uk-flex-middle uk-grid">
                                        @if($company['image_type'] == 'image')
                                            <div
                                                class="uk-width-1-2@s uk-flex-last uk-flex-first@s portfolio-modal-image-container">
                                                <img uk-img data-src="{{ isset($company['image_'.$lang]) ? $company['image_'.$lang] : $company['image'] }}"
                                                     alt="{{ isset($company['name_'.$lang]) ? $company['name_'.$lang] : $company['name'] }}"
                                                     class="portfolio-modal-image">
                                            </div>
                                        @else
                                            <div
                                                class="uk-width-1-2@s uk-flex-last uk-flex-first@s portfolio-modal-image-container">
                                                <video src="{{ isset($company['image_'.$lang]) ? $company['image_'.$lang] : $company['image'] }}" autoplay loop muted playsinline
                                                       class="portfolio-modal-image"></video>
                                            </div>
                                        @endif
                                        <div class="uk-width-1-2@s portfolio-modal-content">
                                            <div class="uk-width-4-5@s">
                                            <div class="portfolio-modal-header">
                                                <div class="portfolio-modal-meta">
                                                    <div
                                                        class="portfolio-modal-industry-tag industry-{{ $company['industry'] }}">
                                                        <span>{{ $industries[$company['industry']][$lang] }}</span>
                                                    </div>
                                                </div>
                                                <div class="portfolio-modal-logo-wrapper">
                                                    @if(strpos($company['logo'], '/') === 0)
                                                        <img src="{{ $company['logo'] }}" alt="{{ $company['name'] }}"
                                                             class="portfolio-modal-logo">
                                                    @else
                                                        <img src="{{ asset('images/projects/' . $company['logo']) }}"
                                                             alt="{{ $company['name'] }}" class="portfolio-modal-logo">
                                                    @endif
                                                </div>

                                            </div>
                                            <div class="portfolio-modal-title-container">
                                                <h1 class="portfolio-modal-title uk-margin-remove-bottom uk-margin-medium-bottom">{{ isset($company['name_'.$lang]) ? $company['name_'.$lang] : $company['name'] }}</h1>
                                                <div class="portfolio-modal-flag">
                                                    <img
                                                        src="https://hatscripts.github.io/circle-flags/flags/{{ $getCountryCode($company['country']) }}.svg"
                                                        width="32" class="country-flag-icon"
                                                        alt="{{ $company['country'] }}"
                                                        title="{{ $company['country'] }}">
                                                </div>
                                            </div>
                                            <div class="portfolio-modal-description">
                                                {!! $company['description'][$lang] !!}
                                            </div>
                                            <div class="portfolio-modal-buttons">
                                                @if(isset($company['website']))
                                                    <a href="{{ $company['website'] }}" target="_blank"
                                                       class="uk-button uk-button-primary portfolio-modal-button">
                                                        {{ $lang == 'cs' ? 'Navštívit webové stránky' : 'Visit website' }}
                                                    </a>
                                                @endif
                                                <button
                                                    class="uk-button uk-button-default uk-modal-close portfolio-modal-button"
                                                    type="button">
                                                    {{ $lang == 'cs' ? 'Zpět na přehled portfolia' : 'Back to portfolio overview' }}
                                                </button>
                                            </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>

                @if(count($filteredData) == 0)
                    <div class="uk-alert uk-alert-warning">
                        <p>{{ $lang == 'cs' ? 'Žádné společnosti nebyly nalezeny pro vybrané filtry.' : 'No companies found for the selected filters.' }}</p>
                    </div>
                @endif
            </div>
        </div>


    </div>

    @include('front-end.components.footer')
@endsection

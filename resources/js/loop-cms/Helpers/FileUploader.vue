<script setup>
import draggable from 'vuedraggable'; // https://github.com/SortableJS/vue.draggable.next
import {ref, watch} from 'vue';
import debounce from 'lodash/debounce';

const props = defineProps({
    modelValue: {
        type: Object,
        required: true,
    },
});

const dragCount = ref(0);
const model = ref(props.modelValue);

function addFiles(newFiles) {
    [...newFiles.target.files].map((file) => addFile(file));
}

function addFile(file) {
    model.value.push(
        {
            file: null,
            order: model.value.length,
            name: file.name,
            type: file.type,
            size: file.size,
            fileData: file,
        }
    );
}

function getFileSize(size) {
    const fSExt = ['Bytes', 'KB', 'MB', 'GB'];
    let i = 0;

    while (size > 900) {
        size /= 1024;
        i++;
    }

    return `${(Math.round(size * 100) / 100)} ${fSExt[i]}`;
}

function OnDrop(event) {
    [...event.dataTransfer.files].map((file) => addFile(file));
    dragCount.value = 0;
}

function OnDragEnter() {
    dragCount.value++;
}

function OnDragLeave() {
    dragCount.value--;
}

function removeFile(index) {
    model.value.splice(index, 1);
}


</script>

<template>
    <div
        class="uk-placeholder uk-text-center"
        :class="dragCount > 0 ? 'dragging' : ''"
        @dragenter.prevent="OnDragEnter"
        @dragleave.prevent="OnDragLeave"
        @dragover.prevent
        @drop.prevent="OnDrop"
    >
        <draggable
            v-model="model"
            tag="transition-group"
            item-key="order"
            @change="$emit('update:modelValue', model)"
        >
            <template #item="{element, index}">
                <div class="uk-grid uk-grid-medium uk-flex-middle uk-flex">
                    <div class="">
                        <a
                            :href="'/'+element.file"
                            target="_blank"
                        >
                            <div v-if="element.type === 'application/pdf'">
                                <span
                                    uk-icon="icon:  file-pdf"
                                />
                            </div>
                            <div v-else-if="element.type.startsWith('image/')">
                                <span uk-icon="icon: image" />
                            </div>
                            <div v-else>
                                <span uk-icon="icon: file-text" />
                            </div>
                        </a>
                    </div>

                    <div class="uk-width-expand">
                        <input
                            id="form-stacked-text"
                            v-model="element.name"
                            class="uk-input uk-text-small"
                            name="title"
                            placeholder="Some text..."
                            type="text"
                        >
                    </div>
                    <div class="uk-width-small">
                        {{ getFileSize(element.size) }}
                    </div>
                    <div>
                        <button
                            class="uk-button uk-button-small uk-text-small uk-button-danger"
                            @click.prevent="removeFile(index)"
                        >
                            <span
                                uk-icon="icon: trash; ratio: 0.7"
                            />
                        </button>
                    </div>
                </div>
            </template>
        </draggable>

        <div class="uk-margin-large">
            <span uk-icon="icon: cloud-upload" />
            <div class="uk-text-middle uk-margin-small">
                Soubory nahrajete přetažením, nebo zmáčknutím tlačítka ...
            </div>
            <div uk-form-custom>
                <input
                    multiple
                    type="file"
                    @change="addFiles"
                >
                <div class="uk-button-default uk-button">
                    Vybrat soubory
                </div>
            </div>
        </div>
    </div>
</template>


<style lang="scss" scoped>
.uk-placeholder {

    &.dragging {
        background: #fff;
        color: #2196F3;
        border: 1px dashed #2196F3;

        .file-input label {
            background: #2196F3;
            color: #fff;
        }
    }


}
</style>

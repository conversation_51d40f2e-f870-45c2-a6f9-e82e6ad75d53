<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('restaurant_daily_menus', function (Blueprint $table) {
            $table->id();
            $table->string('name', 255)->nullable();
            $table->date('start');
            $table->date('end');
            $table->integer('year');
            $table->integer('week');
            $table->bigInteger('created_by');
            $table->bigInteger('edited_by');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('restaurant_daily_menus');
    }
};

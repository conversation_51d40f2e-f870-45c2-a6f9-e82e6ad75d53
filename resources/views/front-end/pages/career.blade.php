@extends('front-end.layouts.app')

@section('title', __('navigation.career'))

@section('content')

    <div class="uk-background-contain uk-section-default uk-background-center-center career-main-section"
         style="background-color: white; background-image: url('/images/home/<USER>');
         background-position: top center; background-size: 4000px; min-height: 100vh;">

        @include('front-end.components.navbar', ['navMode' => 'black'])

        <div class="uk-container uk-container-large uk-margin-remove-right uk-container-expand-right">
            <!-- Mobile sections container with flexbox ordering -->
            <div class="uk-flex uk-flex-column">
                <!-- Content section -->
                <div class="career-content-section mobile-section-order-2">
                    <article class="uk-grid uk-grid-collapse uk-container-item-padding-remove-right" uk-grid>
                        <div class="uk-grid-item-match uk-flex-middle uk-width-2-5@m">
                            <div class="uk-margin-medium-top uk-margin-large-bottom career-content">
                                <div class="uk-margin-remove-first-child uk-padding-large uk-padding-remove-left uk-padding-remove-bottom">
                                    <h2 class="uk-heading-small uk-margin-small-bottom uk-text-uppercase">
                                        {!! $careerHero['heading'][$lang] !!}
                                    </h2>

                                    <blockquote class="career-quote">
                                        {!! $careerHero['claim'][$lang] !!}
                                    </blockquote>
                                    @foreach($careerHero['content'][$lang]['paragraphs'] as $index => $paragraph)
                                        <p class="career-paragraph">
                                            {!! $paragraph !!}
                                        </p>
                                    @endforeach
                                </div>
                            </div>
                        </div>

                        <div class="uk-width-3-5@m uk-container-item-padding-remove-right career-image-desktop" style="min-height: 100vh;">
                            <div class="uk-background-top-center uk-background-cover"
                                style="background-image: url('/images/career/3.jpg'); background-size: cover; min-height: 100%;">
                            </div>
                        </div>
                    </article>
                </div>

                <!-- Image section (will be first on mobile) -->
                <div class="career-image-section mobile-section-order-1 uk-padding-remove">
                    <div class="career-image-mobile"
                        style="background-image: url('/images/career/3.jpg');">
                    </div>
                </div>
            </div>
        </div>
    </div>

    @include('front-end.components.footer')
@endsection

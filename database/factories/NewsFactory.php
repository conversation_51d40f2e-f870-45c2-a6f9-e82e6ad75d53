<?php

namespace Database\Factories;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class NewsFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'title' => $this->faker->sentence(rand(4, 8)),
            'content' => $this->faker->paragraph(5),
            'created_by' => User::firstOrFail(),
            'edited_by' => User::firstOrFail(),
        ];
    }
}

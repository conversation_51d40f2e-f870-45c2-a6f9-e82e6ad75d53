<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class CareerController extends Controller
{
    public function index()
    {
        // Language (for now hardcoded, future would be from session or URL)
        $lang = locale(); // or 'cs' for Czech

        // Career hero section texts
        $careerHero = [
            'heading' => [
                'en' => 'Start your career',
                'cs' => 'Nastartujte<br/> svou kariéru'
            ],
            'claim' => [
                'en' => 'At BHM group, we offer numerous opportunities for individuals who can make decisive decisions and take full responsibility for their work. Join us and become part of successful projects that shape both Czech and global business.',
                'cs' => 'V BHM group máme mnoho příležitostí pro lidi, kteří se dovedou rozhodovat a za svou práci nést odpovědnost. Přidejte se k nám a staňte se součástí úspěšných projektů, které formují český i globální byznys.'
            ],
            'content' => [
                'en' => [
                    'paragraphs' => [
                        'We are looking for motivated and talented individuals with a passion for investments, the world of finance, and specialists from various fields. Our goal is to develop projects and investments with the potential to grow and achieve exceptional results. Those who succeed with us are capable of making decisions, taking responsibility, and bringing innovative solutions to the table.',
                        'You can be part of our success, with the best employees rewarded with rapid career advancement.',
                        'At BHM group, we offer diverse work within a team of professionals who together create an inspiring and supportive environment. Our employees contribute to the development of businesses and services in promising industries across Europe. Working with us means an opportunity to be part of the group\'s dynamic growth, tackle varied challenges, and develop successful projects with real impact.',
                        'We value talent and ambition, which is why we support the personal and professional growth of every team member.',
                        'Through internships, we also open doors to young talent – students and graduates who want to gain experience on exciting projects and develop their skills.',
                        'Interested in joining our team? Send your CV and cover letter to <a href="mailto:<EMAIL>"><EMAIL></a>.'
                    ]
                ],
                'cs' => [
                    'paragraphs' => [
                        'Hledáme motivované a talentované jedince s vášní pro investice, svět financí i specialisty z různých oborů. Naším cílem je rozvíjet projekty a investice, které mají potenciál růst a dosahovat excelentních výsledků. Uspějí u nás ti, kteří jsou schopni rozhodovat, nést zodpovědnost a přinášet inovativní řešení.',
                        'Můžete se podílet na našem úspěchu, nejlepší pracovníci jsou odměňováni rychlým kariérním růstem.',
                        'V BHM group nabízíme různorodou práci v týmu profesionálů, kteří společně vytvářejí inspirativní a podporující prostředí. Naši zaměstnanci se podílejí na rozvoji podnikání a služeb v perspektivních odvětvích po celé Evropě. Práce u nás znamená příležitost podílet se na dynamickém růstu skupiny, řešit rozmanité pracovní výzvy a rozvíjet úspěšné projekty s reálným dopadem.',
                        'Oceňujeme talent a ambice, a proto podporujeme osobní i profesní růst každého člena týmu.',
                        'Prostřednictvím stáží také otevíráme dveře mladým talentům – studentům a absolventům, kteří chtějí získat zkušenosti na zajímavých projektech a rozvíjet své dovednosti.',
                        'Máte zájem stát se součástí našeho týmu? Pošlete svůj životopis a motivační dopis na e-mailovou adresu <a href="mailto:<EMAIL>"><EMAIL></a>.'
                    ]
                ]
            ]
        ];

        // Menu items (from HomeController)
        $menuItems = [
            [
                'title' => [
                    'en' => 'About Us',
                    'cs' => 'O nás'
                ],
                'url' => '#'
            ],
            [
                'title' => [
                    'en' => 'Team',
                    'cs' => 'Tým'
                ],
                'url' => '#'
            ],
            [
                'title' => [
                    'en' => 'Portfolio',
                    'cs' => 'Portfolio'
                ],
                'url' => '#'
            ],
            [
                'title' => [
                    'en' => 'Press Room',
                    'cs' => 'Press Room'
                ],
                'url' => '#'
            ],
            [
                'title' => [
                    'en' => 'Careers',
                    'cs' => 'Kariéra'
                ],
                'url' => '#'
            ],
            [
                'title' => [
                    'en' => 'Contact',
                    'cs' => 'Kontakt'
                ],
                'url' => '#'
            ]
        ];

        return view('front-end.pages.career', compact(
            'lang',
            'menuItems',
            'careerHero'
        ));
    }
}

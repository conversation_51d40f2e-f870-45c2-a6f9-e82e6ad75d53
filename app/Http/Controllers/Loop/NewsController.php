<?php

namespace App\Http\Controllers\Loop;

use App\Http\Controllers\Controller;
use App\Http\Requests\StoreNewsRequest;
use App\Http\Requests\UpdateNewsRequest;
use App\Models\News;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Request;
use Inertia\Inertia;
use Inertia\Response;

class NewsController extends Controller
{

    const MODULE_NAME = 'News';
    const MODULE_ROUTE = 'news';

    public function index(): Response
    {
        return Inertia::render(self::MODULE_NAME . '/Index', [
            'filters' => Request::only('search'),
            'result' => News::query()
                ->filter(Request::only('search'))
                ->orderBy('id', 'desc')
                ->get(),
        ]);
    }

    public function create(): Response
    {
        return Inertia::render(self::MODULE_NAME . '/Create');
    }


    public function store(StoreNewsRequest $request): RedirectResponse
    {
        $news = News::create($request->validated());

        $news->storeImages($request->safe()->images ?? []);
        $news->storeFiles($request->safe()->media_files ?? []);


        return Redirect::route('loop.' . self::MODULE_ROUTE . '.edit', $news);
    }


    public function edit(News $news)
    {
        $news['editor'] = $news->editor;
        $news['images'] = $news->images()->orderBy('order')->get();
        $news['media_files'] = $news->files()->orderBy('order')->get();

        return Inertia::render(self::MODULE_NAME . '/Edit', [
            'result' => $news,
        ]);
    }

    public function update(News $news, UpdateNewsRequest $request)
    {

        $news->update($request->validated());
        $news->storeImages($request->safe()->images ?? []);
        $news->storeFiles($request->safe()->media_files ?? []);

//
//        foreach ($request->safe()->images as $key => $image) {
//            if (isset($image['fileData'])) { // new file
//                $imageFileName = $image['fileData']->store('th/images', 'public');
//
//                $thImagePath = public_path('storage/' . $imageFileName);
//
//                $manager = new ImageManager(new Driver());
//                $manager->read($thImagePath)->cover(800, 800)->save($thImagePath, 30);
//
//                ImageOptimizer::optimize($thImagePath); // replace with optimized version
//
//            }
//        }


        return Redirect::back();
    }


    public function destroy(News $news)
    {
        $news->deleteImages($news->images()->get()); // TODO: when implement restore, move image deletion to pernament delete method

        $news->delete();

        return Redirect::route('loop.' . self::MODULE_ROUTE . '.index');
    }


    public function restore(News $news)
    {
        $news->restore();

        return Redirect::back();
    }


}

<script setup>
import {ref} from 'vue';
import {Head, Link, router} from '@inertiajs/vue3';
import ApplicationMark from '@/Components/ApplicationMark.vue';
import Banner from '@/Components/Banner.vue';
import Dropdown from '@/Components/Dropdown.vue';
import DropdownLink from '@/Components/DropdownLink.vue';
import NavLink from '@/Components/NavLink.vue';
import ResponsiveNavLink from '@/Components/ResponsiveNavLink.vue';
import loopLogo from '@/Loop/Logo';

defineProps({
    title: String,
});

const showingNavigationDropdown = ref(false);

const switchToTeam = (team) => {
    router.put(route('current-team.update'), {
        team_id: team.id,
    }, {
        preserveState: false,
    });
};

const logout = () => {
    router.post(route('logout'));
};

</script>
<template>
    <div>
        <Head :title="title"/>
        <div>
            <div class="xo-layout-left uk-width-medium uk-background-primary uk-height-viewport uk-light">
                <div class="uk-padding">
                    <Link :href="route('dashboard')">
                        <loopLogo/>
                    </Link>
                    <br> <a href="/">{{ $page.props.loop.app.name }}</a>
                </div>
                <div>
                    <div class="uk-padding uk-padding-remove-top">
                        <ul
                            class="uk-nav-default uk-nav uk-nav-parent-icon"
                            uk-nav
                        >
                            <li class="uk-nav-header">
                                Obsah
                            </li>

                            <NavLink
                                :href="route('loop.restaurant-menus.index')"
                                :active="route().current('loop.restaurant-menus.*')"
                            >
                                Jídelní lístek
                            </NavLink>

                            <NavLink
                                :href="route('loop.restaurant-drinks.index')"
                                :active="route().current('loop.restaurant-drinks.*')"
                            >
                                Nápojový lístek
                            </NavLink>

                            <NavLink
                                :href="route('loop.restaurant-daily-menus.index')"
                                :active="route().current('loop.restaurant-daily-menus.*')"
                            >
                                Denní menu
                            </NavLink>
                        </ul>
                    </div>
                    <div class="uk-position uk-position-bottom">
                        <div class="uk-padding">
                            <!--                            <div class="hidden sm:flex sm:items-center sm:ml-6">-->
                            <!--                                <div class="ml-3 relative">-->
                            <!--                                    &lt;!&ndash; Teams Dropdown &ndash;&gt;-->
                            <!--                                    <Dropdown v-if="$page.props.jetstream.hasTeamFeatures" align="right" width="60">-->
                            <!--                                        <template #trigger>-->
                            <!--                                        <span class="inline-flex rounded-md">-->
                            <!--                                            <button type="button" class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-gray-500 bg-white hover:text-gray-700 focus:outline-none focus:bg-gray-50 active:bg-gray-50 transition ease-in-out duration-150">-->
                            <!--                                                {{ $page.props.auth.user.current_team.name }}-->
                            <!--                                                <svg class="ml-2 -mr-0.5 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">-->
                            <!--                                                    <path stroke-linecap="round" stroke-linejoin="round" d="M8.25 15L12 18.75 15.75 15m-7.5-6L12 5.25 15.75 9" />-->
                            <!--                                                </svg>-->
                            <!--                                            </button>-->
                            <!--                                        </span>-->
                            <!--                                        </template>-->
                            <!--                                        <template #content>-->
                            <!--                                            <div class="w-60">-->
                            <!--                                                &lt;!&ndash; Team Management &ndash;&gt;-->
                            <!--                                                <div class="block px-4 py-2 text-xs text-gray-400">-->
                            <!--                                                    Manage Team-->
                            <!--                                                </div>-->
                            <!--                                                &lt;!&ndash; Team Settings &ndash;&gt;-->
                            <!--                                                <DropdownLink :href="route('teams.show', $page.props.auth.user.current_team)">-->
                            <!--                                                    Team Settings-->
                            <!--                                                </DropdownLink>-->
                            <!--                                                <DropdownLink v-if="$page.props.jetstream.canCreateTeams" :href="route('teams.create')">-->
                            <!--                                                    Create New Team-->
                            <!--                                                </DropdownLink>-->
                            <!--                                                &lt;!&ndash; Team Switcher &ndash;&gt;-->
                            <!--                                                <template v-if="$page.props.auth.user.all_teams.length > 1">-->
                            <!--                                                    <div class="border-t border-gray-200" />-->
                            <!--                                                    <div class="block px-4 py-2 text-xs text-gray-400">-->
                            <!--                                                        Switch Teams-->
                            <!--                                                    </div>-->
                            <!--                                                    <template v-for="team in $page.props.auth.user.all_teams" :key="team.id">-->
                            <!--                                                        <form @submit.prevent="switchToTeam(team)">-->
                            <!--                                                            <DropdownLink as="button">-->
                            <!--                                                                <div class="flex items-center">-->
                            <!--                                                                    <svg v-if="team.id == $page.props.auth.user.current_team_id" class="mr-2 h-5 w-5 text-green-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">-->
                            <!--                                                                        <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />-->
                            <!--                                                                    </svg>-->
                            <!--                                                                    <div>{{ team.name }}</div>-->
                            <!--                                                                </div>-->
                            <!--                                                            </DropdownLink>-->
                            <!--                                                        </form>-->
                            <!--                                                    </template>-->
                            <!--                                                </template>-->
                            <!--                                            </div>-->
                            <!--                                        </template>-->
                            <!--                                    </Dropdown>-->
                            <!--                                </div>-->
                            <!--                                &lt;!&ndash; Settings Dropdown &ndash;&gt;-->
                            <!--                                <div class="ml-3 relative">-->
                            <!--                                    <Dropdown align="right" width="48">-->
                            <!--                                        <template #trigger>-->
                            <!--                                            <button v-if="$page.props.jetstream.managesProfilePhotos" class="flex text-sm border-2 border-transparent rounded-full focus:outline-none focus:border-gray-300 transition">-->
                            <!--                                                <img class="h-8 w-8 rounded-full object-cover" :src="$page.props.auth.user.profile_photo_url" :alt="$page.props.auth.user.name">-->
                            <!--                                            </button>-->
                            <!--                                            <span v-else class="inline-flex rounded-md">-->
                            <!--                                            <button type="button" class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-gray-500 bg-white hover:text-gray-700 focus:outline-none focus:bg-gray-50 active:bg-gray-50 transition ease-in-out duration-150">-->
                            <!--                                                {{ $page.props.auth.user.name }}-->
                            <!--                                                <svg class="ml-2 -mr-0.5 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">-->
                            <!--                                                    <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 8.25l-7.5 7.5-7.5-7.5" />-->
                            <!--                                                </svg>-->
                            <!--                                            </button>-->
                            <!--                                        </span>-->
                            <!--                                        </template>-->
                            <!--                                        <template #content>-->
                            <!--                                            &lt;!&ndash; Account Management &ndash;&gt;-->
                            <!--                                            <div class="block px-4 py-2 text-xs text-gray-400">-->
                            <!--                                                Manage Account-->
                            <!--                                            </div>-->
                            <!--                                            <DropdownLink :href="route('profile.show')">-->
                            <!--                                                Profile-->
                            <!--                                            </DropdownLink>-->
                            <!--                                            <DropdownLink v-if="$page.props.jetstream.hasApiFeatures" :href="route('api-tokens.index')">-->
                            <!--                                                API Tokens-->
                            <!--                                            </DropdownLink>-->
                            <!--                                            <div class="border-t border-gray-200" />-->
                            <!--                                            &lt;!&ndash; Authentication &ndash;&gt;-->
                            <!--                                            <form @submit.prevent="logout">-->
                            <!--                                                <DropdownLink as="button">-->
                            <!--                                                    Log Out-->
                            <!--                                                </DropdownLink>-->
                            <!--                                            </form>-->
                            <!--                                        </template>-->
                            <!--                                    </Dropdown>-->
                            <!--                                </div>-->
                            <!--                            </div>-->
                            <!--                            &lt;!&ndash; Hamburger &ndash;&gt;-->
                            <!--                            <div class="-mr-2 flex items-center sm:hidden">-->
                            <!--                                <button class="inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 focus:text-gray-500 transition duration-150 ease-in-out" @click="showingNavigationDropdown = ! showingNavigationDropdown">-->
                            <!--                                    <svg-->
                            <!--                                        class="h-6 w-6"-->
                            <!--                                        stroke="currentColor"-->
                            <!--                                        fill="none"-->
                            <!--                                        viewBox="0 0 24 24"-->
                            <!--                                    >-->
                            <!--                                        <path-->
                            <!--                                            :class="{'hidden': showingNavigationDropdown, 'inline-flex': ! showingNavigationDropdown }"-->
                            <!--                                            stroke-linecap="round"-->
                            <!--                                            stroke-linejoin="round"-->
                            <!--                                            stroke-width="2"-->
                            <!--                                            d="M4 6h16M4 12h16M4 18h16"-->
                            <!--                                        />-->
                            <!--                                        <path-->
                            <!--                                            :class="{'hidden': ! showingNavigationDropdown, 'inline-flex': showingNavigationDropdown }"-->
                            <!--                                            stroke-linecap="round"-->
                            <!--                                            stroke-linejoin="round"-->
                            <!--                                            stroke-width="2"-->
                            <!--                                            d="M6 18L18 6M6 6l12 12"-->
                            <!--                                        />-->
                            <!--                                    </svg>-->
                            <!--                                </button>-->
                            <!--                            </div>-->
                            <ul
                                class="uk-nav-default uk-nav uk-nav-parent-icon"
                                uk-nav
                            >
                                <li class="uk-nav-header">
<!--                                    <DropdownLink :href="route('profile.show')">-->
<!--                                        {{ $page.props.auth.user.name }} {{ $page.props.auth.user.email }}-->
<!--                                    </DropdownLink>-->
                                    {{ $page.props.auth.user.name }} {{ $page.props.auth.user.email }}
                                </li>
                                <li>
                                    <DropdownLink v-if="$page.props.jetstream.hasApiFeatures"
                                                  :href="route('api-tokens.index')">
                                        API Tokens
                                    </DropdownLink>
                                </li>
                                <li class="uk-margin-small-top">
                                    <!-- Authentication -->
                                    <form @submit.prevent="logout">
                                        <DropdownLink as="button">
                                            <span
                                                class="uk-margin-small-righ"
                                                uk-icon="icon: sign-out"
                                            /> Odhlásit se
                                        </DropdownLink>
                                    </form>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="xo-layout-right">
                <div class="uk-container uk-container-large">
                    <header>
                        <div class="uk-padding">
                            <div class="uk-child-width-expand@s uk-flex-top uk-grid">
                                <div>
                                    <div class="uk-flex uk-flex-wrap ">
                                        <div class="uk-width-1-1">
                                            <h2 class="uk-h3">
                                                {{ title }}
                                            </h2>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <hr>
                            <slot
                                v-if="$slots.header"
                                name="header"
                            />
                        </div>
                    </header>
                    <main class="uk-padding uk-padding-remove-top">
                        <slot/>
                    </main>
                </div>
            </div>
        </div>
    </div>
</template>

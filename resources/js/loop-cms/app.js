import './bootstrap';
import('./Uikit/uikit');

import { createApp, h } from 'vue';
import { createInertiaApp } from '@inertiajs/vue3';
import { ZiggyVue } from '../../../vendor/tightenco/ziggy/dist/vue.m';

//const appName = import.meta.env.VITE_APP_NAME || 'Laravel';
const appName = 'Laravel';

createInertiaApp({
    title: (title) => `${title} - ${appName}`,
    resolve: name => require(`./Pages/${name}`),
    setup({ el, App, props, plugin }) {
        return createApp({ render: () => h(App, props) })
            .use(plugin)
            .use(ZiggyVue)
            .mount(el);
    },
    progress: {
        color: '#4B5563',
    },
});

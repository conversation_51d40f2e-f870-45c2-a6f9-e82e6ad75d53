// Modern People Section Styling

// Team grid layout
.team-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
    margin-top: 3rem;

    @media (min-width: 640px) {
        grid-template-columns: repeat(2, 1fr);
    }

    @media (min-width: 960px) {
        grid-template-columns: repeat(3, 1fr);
    }

    @media (min-width: 1200px) {
        grid-template-columns: repeat(4, 1fr);
    }
}

// Team member card
.team-member-card {
    position: relative;
    background-color: rgba(255, 250, 250, 0.1);
    backdrop-filter: blur(2px);
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease, box-shadow 0.3s ease;

    a {
        text-decoration: none;
        color: inherit;
    }

    &:hover {
        //transform: translateY(-5px);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        background-color: rgba(255, 250, 250, 0.4);

        .team-member-info {
            //background-color: rgba(189, 159, 87, 0.05);
        }

        .team-member-more {
            opacity: 1;
        }

        // Zvětšíme o<PERSON> ho<PERSON>, ale <PERSON>ach<PERSON> transform-origin
        .team-member-image {
            //transform: scale(1.3);

            // Speciální případy jsou řešeny individuálně u každého člena týmu
        }
    }

    // Fix for Safari and other browsers
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
}

// Team member image container
.team-member-image-container {
    position: relative;
    padding-bottom: 100%;
    width: 100%;
    overflow: hidden;
    //background-color: #fff;
    margin-bottom: 0.5rem;
}

// Team member image wrapper - this is where we apply the mask
.team-member-image-wrapper {
    position: absolute;
    top: 10%;
    left: 10%;
    width: 80%;
    height: 80%;
    overflow: hidden;

    // Apply mask to the wrapper
    mask-image: url('/images/squircle-mask.svg');
    -webkit-mask-image: url('/images/squircle-mask.svg');
    mask-size: contain;
    -webkit-mask-size: contain;
    mask-repeat: no-repeat;
    -webkit-mask-repeat: no-repeat;
    mask-position: center;
    -webkit-mask-position: center;
}

// Team member image - this is where we apply the transforms
.team-member-image {
    position: absolute;
    //top: 0;
    //left: 0;
    //width: 100%;
    //height: 100%;
    //object-fit: cover;
    //transform: scale(1.2); // Default scale
    //transition: transform 0.4s ease-out;

    // Default transform origin
    //transform-origin: 50% -20%;
}

// Team member info section
.team-member-info {
    padding: 1.25rem;
    text-align: center;
    //background-color: #ffffff;
    transition: background-color 0.3s ease;
    //border-top: 1px solid rgba(0, 0, 0, 0.03);
}

// Team member name
.team-member-name {
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0 0 0.5rem 0;
    color: #333;
}

// Team member position
.team-member-position {
    font-size: 0.9rem;
    color: #666;
    margin: 0;
}

// Team member more link
.team-member-more {
    display: inline-block;
    margin-top: 0.75rem;
    font-size: 0.85rem;
    color: #bd9f57;
    opacity: 0;
    transition: opacity 0.3s ease, transform 0.3s ease;
    position: relative;
    padding-right: 20px;

    &:after {
        content: '→';
        position: absolute;
        right: 0;
        top: 0;
        transition: transform 0.3s ease;
    }

    &:hover {
        text-decoration: underline;

        &:after {
            transform: translateX(5px);
        }
    }
}

// Modal styling
.team-modal {
    background-color: #ffffff;

    .team-modal-close {
        position: absolute;
        top: 1.5rem;
        right: 1.5rem;
        z-index: 10;
    }

    .team-modal-image {
        position: relative;
        height: 100%;
        background-size: cover;
        background-position: center top;
    }

    .team-modal-content {
        padding: 3rem;
    }

    .team-modal-name {
        font-size: 2rem;
        font-weight: 600;
        margin: 0 0 0.5rem 0;
        color: #333;
    }

    .team-modal-position {
        font-size: 1rem;
        color: #666;
        margin: 0 0 2rem 0;
    }

    .team-modal-bio {
        color: #333;
        line-height: 1.6;
    }

    .team-modal-back {
        display: inline-block;
        margin-top: 2rem;
        padding: 0.75rem 1.5rem;
        background-color: #f5f5f5;
        color: #333;
        border-radius: 4px;
        transition: background-color 0.3s ease;

        &:hover {
            background-color: #e5e5e5;
        }
    }
}

// Individual transform origins for team members
.team-member-image.krsek {
    transform-origin: 70% -15%;
}

.team-member-image.cerny {
    transform-origin: 50% -27%;
}

.team-member-image.zychova {
    transform-origin: 10% -25%;
}

.team-member-image.kacena {
    transform-origin: 60% -20%;
}

.team-member-image.mynar {
    transform-origin: 34% -26%;
}

.team-member-image.rubes {
    transform-origin: 45% -30%;
}

.team-member-image.bokor {
    transform-origin: 40% -30%;
}

.team-member-image.vladar {
    transform-origin: 60% -20%;
}

.team-member-image.chaloupka {
    transform-origin: 70% -30%;
}

.team-member-image.kotyza {
    transform-origin: 35% -20%;
}

.team-member-image.betkova {
    transform-origin: 50% -30%;
}

.team-member-image.izak {
    transform-origin: 110% -30%;
}

.team-member-image.stranovsky {
    transform-origin: 55% -30%;
}

.team-member-image.harant {
    transform-origin: 65% -30%;
}

.team-member-image.novacek {
    transform-origin: 50% -24%;
}

.team-member-image.zadrapa {
    transform-origin: 70% -34%;
}

.team-member-image.marklin {
    transform-origin: 25% -34%;
}

.team-member-image.michal-prause {
    transform-origin: 40% -40%;
}

.team-member-image.lucie-dofkova {
    transform-origin: -14% -32%;
}

.team-member-image.robert-doucha {
    transform-origin: 50% 22%;
    //transform: scale(0.9);

    // Upravíme hover efekt pro tento konkrétní případ
    .team-member-card:hover & {
        //transform: scale(1.0);
    }
}

.team-member-image.tuomas-hooli {
    transform-origin: 64% 53%;
    transform: scale(1.0);

    // Upravíme hover efekt pro tento konkrétní případ
    .team-member-card:hover & {
        transform: scale(1.1);
    }
}

.team-member-image.markus-keussen {
    transform-origin: 34% -35%;
}

.team-member-image.jan-svejda {
    transform-origin: 54% -30%;
}

.team-member-image.peter-siso {
    transform-origin: 77% -31%;
}

.team-member-image.miroslav-tabernaus {
    transform-origin: 21% -30%;
}

.team-member-image.filip-holubovsky {
    transform-origin: 45% -35%;
}

.team-member-image.cerna {
    transform-origin: 45% -38%;
}

.team-member-image.kula {
    transform-origin: 25% -30%;
}

.team-member-image.liskova {
    transform-origin: 135% -28%;
}

.team-member-image.sanitrik {
    transform-origin: 55% -33%;
}

.team-member-image.tison {
    transform-origin: 42% -37%;
}

.team-member-image.takimci {
    transform-origin: 52% -27%;
}

import anime from 'animejs';

document.addEventListener('DOMContentLoaded', () => {
    // Get all stat value elements
    const statElements = document.querySelectorAll('.stat-value');
    const hpContentElement = document.getElementById('bhm-hp-content');

    if (statElements.length === 0) return;

    // Initialize all stat elements with zero
    statElements.forEach(element => {
        const finalValue = element.getAttribute('data-value');
        // Parse the value to determine if it's a number, currency, or has a suffix
        const prefix = finalValue.match(/^[^0-9]*/)[0] || '';
        const suffix = finalValue.match(/[^0-9]*$/)[0] || '';

        // Set initial value to 0 with prefix/suffix
        element.innerHTML = prefix + '0' + suffix;
    });

    // Track which elements have been animated
    const animatedElements = new Set();

    // Function to check if an element is covered by any other element
    function isElementCovered(element) {
        const rect = element.getBoundingClientRect();

        // Check multiple points on the element to determine if it's covered
        // We'll check the center and the four corners of the element
        const points = [
            // Center point
            { x: rect.left + rect.width / 2, y: rect.top + rect.height / 2 },
            // Top-left corner
            { x: rect.left + 5, y: rect.top + 5 },
            // Top-right corner
            { x: rect.right - 5, y: rect.top + 5 },
            // Bottom-left corner
            { x: rect.left + 5, y: rect.bottom - 5 },
            // Bottom-right corner
            { x: rect.right - 5, y: rect.bottom - 5 }
        ];

        // Check if any of these points are covered by another element
        for (const point of points) {
            // Get the topmost element at this point
            const topElement = document.elementFromPoint(point.x, point.y);

            // If the topmost element is null (outside viewport) or not our element or its descendant,
            // then our element is covered at this point
            if (!topElement || (!element.contains(topElement) && !topElement.contains(element))) {
                // Check if the point is within the viewport before concluding it's covered
                if (point.x >= 0 && point.x <= window.innerWidth &&
                    point.y >= 0 && point.y <= window.innerHeight) {
                    return true; // Element is covered at this point
                }
            }
        }

        // If we get here, the element is not covered at any of the checked points
        return false;
    }

    // Function to animate a stat element
    function animateStatElement(element) {
        if (animatedElements.has(element)) return;

        const finalValue = element.getAttribute('data-value');

        // Parse the value to determine if it's a number, currency, or has a suffix
        let numericValue = finalValue.replace(/[^0-9.]/g, '');
        const prefix = finalValue.match(/^[^0-9]*/)[0] || '';
        const suffix = finalValue.match(/[^0-9]*$/)[0] || '';

        // Animate the number
        anime({
            targets: element,
            innerHTML: [0, parseFloat(numericValue)],
            easing: 'easeInOutExpo',
            duration: 3000,
            round: 1, // Round to whole numbers
            begin: () => {
                // Mark as animated
                animatedElements.add(element);
            },
            update: function(anim) {
                // Update with current value plus prefix/suffix
                element.innerHTML = prefix + Math.round(anim.animations[0].currentValue) + suffix;
            }
        });
    }

    // Function to check and animate visible elements that are not covered by any other element
    function checkAndAnimateElements() {
        statElements.forEach(element => {
            if (!animatedElements.has(element)) {
                const rect = element.getBoundingClientRect();
                const isVisible = rect.top < window.innerHeight && rect.bottom > 0;

                // Animate when the element is visible and not covered by any other element
                if (isVisible && !isElementCovered(element)) {
                    animateStatElement(element);
                }
            }
        });
    }

    // Create an Intersection Observer to detect when stats are visible
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            // If the element is in the viewport
            if (entry.isIntersecting) {
                const element = entry.target;

                // Only animate if the element is not covered by any other element
                if (!isElementCovered(element)) {
                    animateStatElement(element);
                }
            }
        });
    }, {
        threshold: 0 // Trigger as soon as any part of the element becomes visible
    });

    // Observe all stat elements
    statElements.forEach(stat => {
        observer.observe(stat);
    });

    // Add scroll event listener to check for overlap changes
    window.addEventListener('scroll', checkAndAnimateElements, { passive: true });

    // Initial check
    checkAndAnimateElements();
});

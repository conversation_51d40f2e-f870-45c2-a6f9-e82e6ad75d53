<script setup>
import AppLayout from '@/Layouts/AppLayout.vue';
import {useForm} from '@inertiajs/vue3'
import UIkit from 'uikit';
import ImageUploader from '@/Helpers/ImageUploader';
import FileUploader from '@/Helpers/FileUploader';
import ActionMessage from '@/Components/ActionMessage';
import InputError from '@/Components/InputError';
import Tiptap from '@/Components/Tiptap';
import DateTime from '@/Components/DateTime';

const textTitle = 'Editovat novinku';
const textDeleteConfirm = 'Opravdu chcete novinku smazat?';

const routeUpdate = 'loop.news.update';
const routeDestroy = 'loop.news.destroy';

const props = defineProps({
    result: Object,
});


const form = useForm({
    id: props.result.id,
    title: props.result.title,
    content: props.result.content,
    images: props.result.images,
    media_files: props.result.media_files,
    _method: 'PUT',
});

function update() {
    form.post(route(routeUpdate, form.id), {
        onSuccess: (response) => {
            // form.images = response.props.result.images;
            UIkit.notification({message: 'Uloženo', status: 'success'});
        },
        onError: (errors) => {
            console.log(errors);
            UIkit.notification({message: errors, status: 'success'});
        }
    });
}

function destroy() {

    UIkit.modal.confirm(textDeleteConfirm).then(function () {
        form.delete(route(routeDestroy, form), {
            onSuccess: (response) => {
                UIkit.notification({message: 'Smazáno', status: 'success'});
            }
        });
    });

}

function formatDate(dateString) {
    const date = new Date(dateString);
    // Then specify how you want your dates to be formatted
    return new Intl.DateTimeFormat('cs-CZ', {timeStyle: 'short', dateStyle: 'long',}).format(date);
}
</script>
<template>
    <app-layout :title="textTitle">
        <template #header>
            <div
                class="uk-child-width-expand@s uk-margin-small-top uk-flex-middle"
                uk-grid
            >
                <div class="uk-width-expand">
                    <div
                        class="uk-child-width-expand@s uk-flex uk-flex-middle"
                        uk-grid
                    >
                        <div class="uk-width-expand">
                            <div v-if="form.id">
                                Naposledy uloženo  <date-time :date="result.updated_at" /> uživatelem {{ result.editor.name }}
                            </div>
                        </div>
                    </div>
                </div>
                <div class="uk-width-auto">
                    <button
                        class="uk-button uk-button-default uk-margin-right"
                        :loading="false"
                        type="submit"
                        @click.prevent="update()"
                    >
                        Uložit
                    </button>
                    <button
                        class="uk-button uk-button-danger"
                        type="submit"
                        @click.prevent="destroy()"
                    >
                        <span uk-icon="icon: trash;"/>
                    </button>
                </div>
            </div>
        </template>



        <form
            class="uk-form-stacked"
            @submit.prevent="update"
        >
            <div class="uk-margin">
                <label
                    class="uk-form-label"
                    for="form-stacked-text"
                >Titulek</label>
                <div class="uk-form-controls">
                    <input
                        id="form-stacked-text"
                        v-model="form.title"
                        class="uk-input uk-form-large"
                        name="title"
                        placeholder="Zadejte titulek stránky..."
                        type="text"
                    >
                    <InputError :message="form.errors.title"/>
                </div>
            </div>
            <div class="uk-margin">
                <label
                    class="uk-form-label"
                    for="content"
                >Text novinky</label>
                <div class="uk-form-controls">
                    <tiptap v-model="form.content" />
                    <InputError :message="form.errors.content"/>
                </div>
                <ImageUploader
                    v-model="form.images"
                />
                <FileUploader
                    v-model="form.media_files"
                />
            </div>
            <div class="">
                <ActionMessage
                    :on="form.recentlySuccessful"
                >
                    Uspěšně uloženo
                </ActionMessage>

                <button
                    class="uk-button uk-button-primary uk-button-large"
                    :loading="false"
                    type="submit"
                >
                    Uložit
                </button>
            </div>
        </form>
    </app-layout>
</template>

<script setup>

import AppLayout from '@/Layouts/AppLayout.vue';
import NavLink from '@/Components/NavLink.vue';

</script>


<template>
    <app-layout title="Dashboard">
        <h1 class="uk-h2 uk-margin-remove-bottom">
            Pěk<PERSON><PERSON> den, {{ $page.props.loop.user.vokativ }}
        </h1>
        <p class="uk-margin-large-bottom uk-margin-small-top">
            Pro rychlejší přidání obsahu využijte některou z
            následujících možností.
        </p>

        <div
            class="uk-child-width-expand@s "
            uk-grid
        >
            <div>
                <article class="uk-comment uk-margin-medium-bottom">
                    <header
                        class="uk-comment-hea7der uk-grid-medium uk-flex-top"
                        uk-grid
                    >
                        <div class="uk-width-auto">
                            <span uk-icon="icon: thumbnails; " />
                        </div>
                        <div class="uk-width-expand">

                            <NavLink
                                :href="route('loop.restaurant-menus.index')"
                                :active="route().current('loop.restaurant-menus.*')"
                            >
                                J<PERSON><PERSON><PERSON><PERSON> l<PERSON>
                            </NavLink>

                            <NavLink
                                :href="route('loop.restaurant-drinks.index')"
                                :active="route().current('loop.restaurant-drinks.*')"
                            >
                                Nápojový lístek
                            </NavLink>

                            <NavLink
                                :href="route('loop.restaurant-daily-menus.index')"
                                :active="route().current('loop.restaurant-daily-menus.*')"
                            >
                                Denní menu
                            </NavLink>
                        </div>
                    </header>
                </article>
            </div>
        </div>
    </app-layout>
</template>

<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateNewsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'title' => ['required', 'max:100'],
            'content' => ['required'],
            'images' => ['nullable', 'array'],
            'media_files' => ['nullable', 'array'],

            /*'images.name' => 'nullable|string',
            'images.order' => 'integer',
            'files.name' => 'nullable|string',
            'files.order' => 'integer',*/
        ];
    }
}

<script setup>
import AppLayout from '@/Layouts/AppLayout.vue';
import UIkit from 'uikit';
import ImageUploader from '@/Helpers/ImageUploader';
import FileUploader from '@/Helpers/FileUploader';
import {useForm} from "@inertiajs/vue3";
import ActionMessage from '@/Components/ActionMessage';
import InputError from '@/Components/InputError';
import Tiptap from '@/Components/Tiptap';
import DatePicker from 'vue-datepicker-next';
import 'vue-datepicker-next/index.css';

const textTitle = 'Přidat událost';

const routeStore = 'loop.event.store';


const newDateObj = {
    id: null,
    date: '',
    start_at: null,
    end_at: null
};

const form = useForm({
    title: null,
    content: null,
    images: [],
    media_files: [],
    dates: [newDateObj],
    price: null,
    place: null,
    ticket_url: null,
});


function store() {
    form.post(route(routeStore), {
        onSuccess: (response) => {
            UIkit.notification({message: 'U<PERSON><PERSON>eno', status: 'success'});
        }
    });
}

function addDate() {
    form.dates.push(newDateObj);
}

function removeDate(index) {
    form.dates.splice(index, 1)
}
</script>
<template>
    <app-layout :title="textTitle">
        <template #header>
            <div
                class="uk-child-width-expand@s uk-margin-small-top uk-flex-middle"
                uk-grid
            >
                <div class="uk-width-expand">
                    <div
                        class="uk-child-width-expand@s uk-flex uk-flex-middle"
                        uk-grid
                    >
                        <div class="uk-width-expand">
                            <div v-if="form.id">
                                Naposledy uloženo {{ formatDate(news.updated_at) }} uživatelem {{ news.editor.name }}
                            </div>
                        </div>
                    </div>
                </div>
                <div class="uk-width-auto">
                    <button
                        class="uk-button uk-button-default uk-margin-right"
                        :loading="false"
                        type="submit"
                        @click.prevent="store()"
                    >
                        Uložit
                    </button>
                </div>
            </div>
        </template>
        <div
            v-if="form.hasErrors"
            class=""
            style="color: red"
        >
            Opravte následující chyby
        </div>
        <form
            class="uk-form-stacked"
            @submit.prevent="store"
        >
            <div class="uk-margin">
                <label
                    class="uk-form-label"
                    for="form-stacked-text"
                >Titulek</label>
                <div class="uk-form-controls">
                    <input
                        id="form-stacked-text"
                        v-model="form.title"
                        class="uk-input uk-form-large"
                        name="title"
                        placeholder="Zadejte titulek stránky..."
                        type="text"
                    >
                    <InputError :message="form.errors.title"/>
                </div>
            </div>

            <div class="uk-margin">
                <label class="uk-form-label" for="form-stacked-text">Vstupné</label>
                <div class="uk-form-controls">
                    <input class="uk-input" id="form-stacked-text" name="title"
                           placeholder="Zadejte vstupné, pokud je..."
                           type="text"
                           v-model="form.price">
                    <InputError :message="form.errors.price"/>
                </div>
            </div>
            <div class="uk-margin">
                <label class="uk-form-label" for="form-stacked-text">Adresa pro vstupenky</label>
                <div class="uk-form-controls">
                    <input class="uk-input" id="form-stacked-text" name="title"
                           placeholder="Zadejte adresu kde lze zakoupit lístek, pokud je..."
                           type="text"
                           v-model="form.ticket_url">
                    <InputError :message="form.errors.ticket_url"/>
                </div>
            </div>
            <div class="uk-margin">
                <label class="uk-form-label" for="form-stacked-text">Místo události</label>
                <div class="uk-form-controls">
                    <input class="uk-input" id="form-stacked-text" name="title"
                           placeholder="Zadejte místo konání události, pokud je..."
                           type="text" v-model="form.place">
                    <InputError :message="form.errors.place"/>
                </div>
            </div>
            <div class="uk-margin">
                <label
                    class="uk-form-label"
                    for="content"
                >Text novinky</label>
                <div class="uk-form-controls">
                    <tiptap v-model="form.content"/>
                    <InputError :message="form.errors.content"/>
                </div>
            </div>
            <div class="uk-margin">
                <label class="uk-form-label" for="form-stacked-text">Data události</label>
                <div class="uk-form-controls">
                    <div
                        class="uk-margin-small"
                        v-for="(date, index) in form.dates"
                        v-bind="{date}"
                        :key="date.id">
                        <date-picker v-model:value="date.date" format="D. M. YYYY" placeholder="Den"
                                     valueType="YYYY-MM-DD"></date-picker>
                        <date-picker v-model:value="date.start_at" valueType="format" placeholder="Od kolika hodin?"
                                     type="time"></date-picker>
                        <date-picker v-model:value="date.end_at" valueType="format" placeholder="Do kolika hodin?"
                                     type="time"></date-picker>
                        <button class="uk-button uk-button-small uk-button-danger" @click="removeDate(index)"><span
                            uk-icon="icon: trash;"></span></button>
                    </div>
                    <button class="uk-margin uk-button" @click="addDate"> Přidat datum</button>
                </div>
            </div>

            <ImageUploader
                v-model="form.images"
            />
<!--            <FileUploader-->
<!--                v-model="form.media_files"-->
<!--            />-->
            <div class="">
                <ActionMessage
                    :on="form.recentlySuccessful"
                >
                    Uspěšně uloženo
                </ActionMessage>
                <button
                    class="uk-button uk-button-primary uk-button-large"
                    :loading="false"
                    type="submit"
                >
                    Uložit
                </button>
            </div>
        </form>
    </app-layout>
</template>

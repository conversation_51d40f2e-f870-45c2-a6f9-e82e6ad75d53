<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class PortfolioController extends Controller
{
    // Industry type constants
    const INDUSTRY_MEDTECH = 'medtech';
    const INDUSTRY_CLEANTECH = 'cleantech';
    const INDUSTRY_RENEWABLES = 'renewables';
    const INDUSTRY_TECH_STARTUPS = 'tech-startups';
    const INDUSTRY_HOSPITALITY = 'hospitality';
    const INDUSTRY_RESIDENTIALS = 'residentials';
    const INDUSTRY_LOGISTICS = 'logistics';
    const INDUSTRY_RETAIL = 'retail';
    const INDUSTRY_OTHER = 'other';
    /**
     * Display the portfolio page.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        // Get the current language
        $lang = locale();

        // Portfolio page content
        $portfolioContent = [
            'cs' => [
                'title' => 'NAŠE PORTFOLIO',
                'description' => 'BHM group spravuje aktiva v hodnotě 1 miliardy eur, působí ve více než 20 evropských zemích a zaměstnává přes 2000 lidí po celé Evropě. Skupina aktivně řídí více než 30 investic napříč různými odvětvími. Zde si můžete vyhledat více informací o portfoliu společnosti a projektech, do kterých investuje.'
            ],
            'en' => [
                'title' => 'OUR PORTFOLIO',
                'description' => 'BHM group manages assets worth €1 billion, operates in over 20 European countries, and employs more than 2,000 people across Europe. The group actively oversees more than 30 investments across various industries. Here, you can find more information about the company\'s portfolio and the projects it invests in.'
            ]
        ];

        // Define industries
        $industries = [
            self::INDUSTRY_MEDTECH => [
                'en' => 'MedTech',
                'cs' => 'MedTech'
            ],
            self::INDUSTRY_CLEANTECH => [
                'en' => 'CleanTech',
                'cs' => 'CleanTech'
            ],
            self::INDUSTRY_RENEWABLES => [
                'en' => 'Renewables',
                'cs' => 'Renewables'
            ],
            self::INDUSTRY_TECH_STARTUPS => [
                'en' => 'Tech Startups',
                'cs' => 'Tech Startups'
            ],
            self::INDUSTRY_HOSPITALITY => [
                'en' => 'Hospitality',
                'cs' => 'Hospitality'
            ],
            self::INDUSTRY_RESIDENTIALS => [
                'en' => 'Residentials',
                'cs' => 'Residentials'
            ],
            self::INDUSTRY_LOGISTICS => [
                'en' => 'Logistics',
                'cs' => 'Logistics'
            ],
            self::INDUSTRY_RETAIL => [
                'en' => 'Retail',
                'cs' => 'Retail'
            ],
            self::INDUSTRY_OTHER => [
                'en' => 'Other',
                'cs' => 'Ostatní'
            ]
        ];

        // Get mock portfolio data
        $portfolioData = $this->getMockPortfolioData();

        // Get filters from request
        $industryFilter = $request->query('industry', 'all');
        $countryFilter = $request->query('country', 'all');

        // Filter the data based on the filters
        $filteredData = $this->filterPortfolioData($portfolioData, $industryFilter, $countryFilter);

        // Get unique countries for the filter dropdown
        $countries = $this->getUniqueCountries($portfolioData);


        return view('front-end.pages.portfolio', compact(
            'lang',
            'portfolioContent',
            'industries',
            'portfolioData',
            'filteredData',
            'industryFilter',
            'countryFilter',
            'countries',
        ))->with('getCountryCode', [$this, 'getCountryCode']);
    }

    /**
     * Get portfolio data from JSON file.
     *
     * @return array
     */
    private function getMockPortfolioData()
    {
        // Get data from JSON file
        $jsonPath = public_path('json/portfolio-companies.json');

        if (file_exists($jsonPath)) {
            $jsonContent = file_get_contents($jsonPath);
            if ($jsonContent === false) {
                // Log error if file can't be read
                \Log::error('Could not read portfolio-companies.json file');
                return [];
            }

            $data = json_decode($jsonContent, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                // Log error if JSON is invalid
                \Log::error('Invalid JSON in portfolio-companies.json: ' . json_last_error_msg());
                return [];
            }

            if (!is_array($data)) {
                // Log error if decoded data is not an array
                \Log::error('Decoded JSON is not an array in portfolio-companies.json');
                return [];
            }

            return $data;
        }

        // Fallback to empty array if file doesn't exist
        \Log::warning('portfolio-companies.json file does not exist');
        return [];
    }

    /**
     * Filter portfolio data based on industry and country filters.
     *
     * @param array|null $data
     * @param string $industryFilter
     * @param string $countryFilter
     * @return array
     */
    private function filterPortfolioData($data, $industryFilter, $countryFilter)
    {
        // Ensure data is an array
        if (!is_array($data) || empty($data)) {
            \Log::warning('Empty or invalid data passed to filterPortfolioData');
            return [];
        }

        $filtered = array_filter($data, function ($item) use ($industryFilter, $countryFilter) {
            // Check if required keys exist
            if (!isset($item['industry']) || !isset($item['country'])) {
                \Log::warning('Portfolio item missing required keys: ' . json_encode($item));
                return false;
            }

            $industryMatch = $industryFilter === 'all' || $item['industry'] === $industryFilter;
            $countryMatch = $countryFilter === 'all' || $item['country'] === $countryFilter;

            return $industryMatch && $countryMatch;
        });

        // Sort the filtered data alphabetically by company name
        return $this->sortPortfolioData($filtered);
    }

    /**
     * Get unique countries from portfolio data.
     *
     * @param array|null $data
     * @return array
     */
    private function getUniqueCountries($data)
    {
        // Ensure data is an array
        if (!is_array($data) || empty($data)) {
            \Log::warning('Empty or invalid data passed to getUniqueCountries');
            return [];
        }

        $countries = array_map(function ($item) {
            // Check if country key exists
            if (!isset($item['country'])) {
                \Log::warning('Portfolio item missing country key: ' . json_encode($item));
                return 'Unknown';
            }
            return $item['country'];
        }, $data);

        $uniqueCountries = array_unique($countries);

        // Sort countries alphabetically
        sort($uniqueCountries);

        return $uniqueCountries;
    }

    /**
     * Sort portfolio data by specified criteria (default: alphabetically by name).
     *
     * @param array $data The data to sort
     * @param string $sortBy The field to sort by (default: 'name')
     * @param string $direction The sort direction (default: 'asc')
     * @return array Sorted data
     */
    private function sortPortfolioData($data, $sortBy = 'name', $direction = 'asc')
    {
        // Ensure data is an array
        if (!is_array($data) || empty($data)) {
            return [];
        }

        // Convert to array with numeric keys to preserve after sorting
        $data = array_values($data);

        // Sort the data
        usort($data, function ($a, $b) use ($sortBy, $direction) {
            // First check if sort_order exists in either item
            if (isset($a['sort_order']) && isset($b['sort_order'])) {
                // If both have sort_order, compare them
                if ($a['sort_order'] != $b['sort_order']) {
                    return $a['sort_order'] - $b['sort_order'];
                }
            } else if (isset($a['sort_order'])) {
                // If only $a has sort_order, it should come after $b
                return 1;
            } else if (isset($b['sort_order'])) {
                // If only $b has sort_order, it should come after $a
                return -1;
            }

            // Check for relative_shift parameter
            if (isset($a['relative_shift']) && isset($b['id']) && $a['relative_shift'] === $b['id']) {
                // If A should be shifted relative to B, place A before B
                return -1;
            }
            if (isset($b['relative_shift']) && isset($a['id']) && $b['relative_shift'] === $a['id']) {
                // If B should be shifted relative to A, place B before A
                return 1;
            }

            // Check if the sort field exists in both items
            if (!isset($a[$sortBy]) || !isset($b[$sortBy])) {
                return 0; // No change in order if field doesn't exist
            }

            // Compare the values
            $comparison = strcmp($a[$sortBy], $b[$sortBy]);

            // Return based on direction
            return $direction === 'asc' ? $comparison : -$comparison;
        });

        return $data;
    }

    /**
     * Convert country name to ISO 3166-1 alpha-2 code for flag display
     *
     * @param string $countryName
     * @return string
     */
    public function getCountryCode($countryName)
    {
        $countryCodes = [
            'Czech Republic' => 'cz',
            'Poland' => 'pl',
            'Germany' => 'de',
            'Denmark' => 'dk',
            'Slovakia' => 'sk',
            'United Kingdom' => 'gb',
            'Finland' => 'fi',
            'Lithuania' => 'lt',
            'Estonia' => 'ee',
            'Unknown' => 'xx'
        ];

        return $countryCodes[$countryName] ?? strtolower(substr($countryName, 0, 2));
    }
}

@php
    // Default to white mode if not specified
    $navMode = $navMode ?? 'white';
    // Get current route name without locale prefix
    $currentRoute = substr(Route::currentRouteName(), 3);

    // Define navigation items
    $navigationItems = [
        ['route' => 'home', 'title_key' => 'home'],
        ['route' => 'about', 'title_key' => 'about_us'],
        ['route' => 'people', 'title_key' => 'people'],
        ['route' => 'portfolio', 'title_key' => 'portfolio'],
        ['route' => 'news', 'title_key' => 'pressroom'],
        ['route' => 'career', 'title_key' => 'career'],
        ['route' => 'contacts', 'title_key' => 'contacts'],
    ];
@endphp

{{-- Mobile Header --}}
{{--<header class="bhm-header-mobile uk-hidden@m tm-header-overlay nav-mode-{{ $navMode }}" uk-inverse="target: .uk-navbar-container" uk-header>--}}
{{--    <div xxx-uk-sticky cls-active="uk-navbar-sticky" sel-target=".uk-navbar-container">--}}
{{--        <div class="uk-navbar-container uk-navbar-transparent uk-position-relative uk-position-z-index-high">--}}
{{--            <div class="uk-container uk-container-expand">--}}
{{--                <nav class="uk-navbar" uk-navbar="{&quot;align&quot;:&quot;left&quot;,&quot;container&quot;:&quot;.bhm-header-mobile &gt; [uk-sticky]&quot;,&quot;boundary&quot;:&quot;.bhm-header-mobile .uk-navbar-container&quot;}">--}}
{{--                    <div class="uk-navbar-left">--}}
{{--                        <a href="{{route(locale().'.home')}}" aria-label="Back to home" class="uk-logo uk-navbar-item">--}}
{{--                            @if($navMode === 'black')--}}
{{--                                <img src="{{ asset('/images/logo.svg') }}" width="120" alt="BHM Group" height="">--}}
{{--                                <img class="uk-logo-inverse" src="{{ asset('/images/logo-white.svg') }}" width="120" alt="BHM Group" height="">--}}
{{--                            @else--}}
{{--                                <img src="{{ asset('/images/logo-white.svg') }}" width="120" alt="BHM Group" height="">--}}
{{--                                <img class="uk-logo-inverse" src="{{ asset('/images/logo.svg') }}" width="120" alt="BHM Group" height="">--}}
{{--                            @endif--}}
{{--                        </a>--}}
{{--                    </div>--}}
{{--                    <div class="uk-navbar-right">--}}
{{--                        <div class="uk-inline-block uk-margin-small-right" style="width: 32px; height: 32px; position: relative;">--}}
{{--                            <a style="color: {{ $navMode === 'black' ? '#0B1215' : '#fff' }}" uk-toggle href="#bhm-menu-dialog" class="uk-navbar-toggle">--}}
{{--                                <div uk-navbar-toggle-icon></div>--}}
{{--                            </a>--}}
{{--                        </div>--}}
{{--                    </div>--}}
{{--                </nav>--}}
{{--            </div>--}}
{{--        </div>--}}
{{--    </div>--}}
{{--</header>--}}

{{-- Desktop Header --}}
<header class="xxx-uk-visible@m page-{{ str_replace('.', '-', $currentRoute) }} nav-mode-{{ $navMode }}" style="position: absolute; top: 0; left: 0; right: 0; z-index: 1000;" uk-header>
    <div class="uk-navbar-container uk-navbar-transparent" xxx-uk-sticky="cls-active: nav-sticky; top: 100vh">
        <div class="uk-container uk-container-large">
            <nav class="uk-navbar" uk-navbar>
                <div class="uk-navbar-left">
                    <a href="{{route(locale().'.home')}}" aria-label="Back to home" class="uk-logo uk-navbar-item">
                        @if($navMode === 'black')
                            <img alt="Logo" loading="eager" style="height: 30px" src="/images/logo.svg">
                        @else
                            <img alt="Logo" loading="eager" style="height: 30px" src="/images/logo-white.svg">
                        @endif
                    </a>
                </div>
                <div class="uk-navbar-right">
                    {{-- Language Switcher --}}
                    <ul class="bhm-lang-switcher uk-grid-small {{ $navMode === 'white' ? 'uk-light' : 'uk-dark' }} uk-flex-inline uk-flex-middle uk-flex-nowrap" uk-grid>
                        <li>
                            <a href="{{ route('cs.home') }}" class="uk-icon-link {{ locale() == 'cs' ? 'uk-badge' : '' }}" uk-icon="">{{ __('navigation.language_cs') }}</a>
                        </li>
                        <li>
                            <a href="{{ route('en.home') }}" class="uk-icon-link {{ locale() == 'en' ? 'uk-badge' : '' }}" uk-icon="">{{ __('navigation.language_en') }}</a>
                        </li>
                    </ul>
                    <a uk-toggle href="#bhm-menu-dialog" class="bhm-navbar-toggle uk-navbar-toggle-animate">
                        <div style="color: {{ $navMode === 'black' ? '#000' : '#fff' }}" uk-navbar-toggle-icon class="uk-navbar-toggle-icon"></div>
                    </a>
                </div>
            </nav>
        </div>
    </div>
</header>

{{-- Navigation Menu Dialog --}}
<div id="bhm-menu-dialog" uk-offcanvas="container: true; overlay: true" mode="slide" flip>
    <div class="uk-offcanvas-bar uk-flex uk-flex-column">
        <button class="uk-offcanvas-close uk-close-large" type="button" uk-close uk-toggle="cls: uk-close-large; mode: media; media: @s"></button>
        <div class="uk-margin-auto-vertical">
            <div class="uk-panel">
                <ul class="uk-nav uk-nav-primary">
                    @foreach($navigationItems as $item)
                        <li class="bhm-nav-item {{ $currentRoute == $item['route'] ? 'uk-active' : '' }}">
                            <a href="{{ route(locale().'.'.$item['route']) }}">
                                {{ __('navigation.' . $item['title_key']) }}
                            </a>
                        </li>
                    @endforeach
                </ul>
            </div>
        </div>
    </div>
</div>

<?php

namespace App\Policies;

use App\Models\RestaurantMenuItem;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class RestaurantMenuItemPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        //
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, RestaurantMenuItem $restaurantMenuItem): bool
    {
        //
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        //
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, RestaurantMenuItem $restaurantMenuItem): bool
    {
        //
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, RestaurantMenuItem $restaurantMenuItem): bool
    {
        //
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, RestaurantMenuItem $restaurantMenuItem): bool
    {
        //
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, RestaurantMenuItem $restaurantMenuItem): bool
    {
        //
    }
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('restaurant_daily_menu_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('restaurant_daily_menu_id')->constrained()->cascadeOnDelete();
            $table->date('menu_date');
            $table->string('name', 255);
            $table->string('price', 32)->nullable();
            $table->string('weight', 10)->nullable();
            $table->integer('type');
            $table->integer('order')->nullable();
            $table->string('number', 10)->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('restaurant_daily_menu_items');
    }
};

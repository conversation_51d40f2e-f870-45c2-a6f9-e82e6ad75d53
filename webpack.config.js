const path = require('path');
const webpack = require('webpack');


module.exports = {
    resolve: {
        alias: {
            '@': path.resolve('resources/js/loop-cms'),
        },
    },
    watchOptions: {
        ignored: /node_modules/
    },
    plugins: [
        new webpack.DefinePlugin({
            __VUE_OPTIONS_API__: 'true',
            __VUE_PROD_DEVTOOLS__: 'false',
            __VUE_PROD_HYDRATION_MISMATCH_DETAILS__: 'false'
        })
    ]
};

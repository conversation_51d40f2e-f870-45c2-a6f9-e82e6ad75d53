// require('./bootstrap');

require('./uikit');
require('./stats-animation');


// open links in new tab
function externalLinks() {
    for (var c = document.getElementsByTagName("a"), a = 0; a < c.length; a++) {
        var b = c[a];

        if (b.getAttribute("target") != "_self") {
            b.getAttribute("href") && b.hostname !== location.hostname && (b.target = "_blank")
        }
    }
};

externalLinks();

//
// import { gsap, Power2, Bounce } from 'gsap';
//
// import { ScrollTrigger } from "gsap/ScrollTrigger";
//
// gsap.registerPlugin(ScrollTrigger);
//
//
// gsap.set('#clippath', {xPercent:-50});
//
// var rotate = gsap.timeline({
//     scrollTrigger:{
//         trigger: "#wrap",
//         scrub: 0.1,
//         start: 'top',
//         end:'+=50',
//     }
// })
//     .to('#green', {
//         rotation:180,
//         duration: 1, ease:'none',
//     })

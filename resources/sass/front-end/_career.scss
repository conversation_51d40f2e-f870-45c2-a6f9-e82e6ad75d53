// Career page styles
.career-content {
    @media (max-width: 959px) {
        margin-top: 0 !important;
        padding-bottom: 20px;
    }
}

.bhm-quote-primary {
    font-size: 24px;
    line-height: 1.4;
    font-weight: 500;
    color: $bhm-gold;
    font-style: italic;
}

.bhm-quote-secondary {
    font-size: 18px;
    line-height: 1.4;
    font-weight: 500;
    color: $bhm-dark-gray;
    font-style: italic;
    border-left: 3px solid $bhm-gold;
    padding-left: 15px;
}

// Career page specific styles
.career-quote {
    font-family: 'Work Sans', sans-serif;
    font-style: italic;
    font-weight: 400;
    color: #0b1215;
    letter-spacing: -1px;
    font-size: 23px;
}

.career-paragraph {
    line-height: 1.6;
    margin-bottom: 20px;
}

// Mobile-specific styles
@media (max-width: 959px) {
    // Mobile section ordering
    .mobile-section-order-1 {
        order: 1;
    }

    .mobile-section-order-2 {
        order: 2;
    }

    // Mobile image styling
    .career-image-section {
        margin: 0;
        padding: 0;
        width: 100vw;
        position: relative;
        left: 50%;
        right: 50%;
        margin-left: -50vw;
        margin-right: -50vw;
    }

    .career-image-mobile {
        height: 50vh;
        width: 100%;
        background-size: cover;
        background-position: left center;
    }

    // Hide desktop image on mobile
    .career-image-desktop {
        display: none;
    }

    // Content styling for mobile
    .career-content-section {
        //padding: 15px 25px;

        .uk-heading-small {
            //font-size: 28px;
            //text-align: center;
            //margin-bottom: 20px;
        }

        .career-quote {
            font-size: 18px;
            margin: 20px 0;
            padding: 15px;
            border-left: 1px solid $bhm-gold;
        }

        .career-paragraph {
            font-size: 16px;
            line-height: 1.6;
            margin-bottom: 15px;
        }

    }
}

// Desktop-specific styles to ensure no changes to desktop view
@media (min-width: 960px) {
    // Hide mobile image on desktop
    .career-image-section {
        display: none;
    }
}

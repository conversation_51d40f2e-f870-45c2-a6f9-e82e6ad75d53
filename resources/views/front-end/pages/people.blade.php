@extends('front-end.layouts.app')

@section('title', __('navigation.people'))

@section('content')
    <div class="uk-section- people-background uk-inverse- uk-section uk-section-large uk-padding-remove-bottom"
         style="background-image: url('/images/home/<USER>'); background-size: 250%; background-position: bottom center;"
         uk-scrollspy="target: [uk-scrollspy-class]; cls: uk-animation-fade; delay: false;">
        @include('front-end.components.navbar', ['navMode' => 'black'])

        <div class="uk-container uk-container-large">
            <div class="uk-grid">

                <div class="uk-width-1-2@s uk-margin-auto">

                    <h2 class="uk-heading-small uk-margin-small-bottom uk-text-uppercase uk-text-center@s">
                        {!! $title[locale()] !!}
                    </h2>

                    <p class="uk-margin" uk-scrollspy-class>
                        {!! $teamDescription[locale()] !!}
                    </p>

                </div>
            </div>

        </div>

        <div class="uk-section uk-padding-remove-top">
            <div class="uk-container uk-container-large">
                <div class="team-grid uk-margin-xlarge-bottom">
                    @foreach ($people as $person)
                        <div class="team-member-card">
                            <a href="#{{$person["slug"]}}-{{$loop->index}}" uk-toggle>
                                <div class="team-member-image-container">
                                    <div class="team-member-image-wrapper">
                                        <img class="team-member-image {{$person["slug"]}}"
                                             src="/images/people/{{$person["slug"]}}.jpg"
                                             alt="{{$person["name"]}}">
                                    </div>
                                </div>
                                <div class="team-member-info">
                                    <h3 class="team-member-name">{{$person["name"]}}</h3>
                                    <p class="team-member-position">{!!$person["position"][locale()]!!}</p>
                                    <span class="team-member-more">{{ locale() == 'cs' ? 'Více informací' : 'More information' }}</span>
                                </div>
                            </a>

                            <div id="{{$person["slug"]}}-{{$loop->index}}" class="uk-modal-full team-modal" uk-modal>
                                <div class="uk-modal-dialog">
                                    <button class="uk-modal-close-full uk-close-large team-modal-close" type="button" uk-close></button>
                                    <div class="uk-grid-collapse uk-flex-middle uk-grid">
                                        <div class="team-modal-image uk-width-1-2@s uk-width-2-5@m uk-flex-last uk-flex-first@s"
                                            style="background-image: url('/images/people/{{$person["slug"]}}.jpg');"
                                            uk-height-viewport></div>
                                        <div class="team-modal-content uk-width-1-2@s uk-width-3-5@m">
                                            <h1 class="team-modal-name">{{$person["name"]}}</h1>
                                            <p class="team-modal-position">{!! $person["position"][locale()] !!}</p>
                                            <div class="team-modal-bio">
                                                {!! $person["longText"][locale()] !!}
                                            </div>
                                            <button
                                                class="uk-button uk-button-default uk-modal-close portfolio-modal-button"
                                                type="button">
                                                {{ locale() == 'cs' ? 'Zpět na přehled týmu' : 'Back to team overview' }}
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>


    </div>


    @include('front-end.components.footer')
@endsection

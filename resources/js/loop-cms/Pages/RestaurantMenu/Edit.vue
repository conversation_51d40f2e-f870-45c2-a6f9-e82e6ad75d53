<script setup>
import AppLayout from '@/Layouts/AppLayout.vue';
import {useForm} from '@inertiajs/vue3'
import UIkit from 'uikit';
import ActionMessage from '@/Components/ActionMessage';
import InputError from '@/Components/InputError';
import DateTime from '@/Components/DateTime';
import Date from '@/Components/Date';

const textTitle = 'Jídelní lístek';
const textDeleteConfirm = 'Opravdu chcete menu smazat?';

const routeUpdate = 'loop.restaurant-menus.update';
const routeDestroy = 'loop.restaurant-menus.destroy';

const props = defineProps({
    result: Object,
});

const newDateObj = {
    id: null,
    number: '',
    name: '',
    price: '',
    description: '',
};

const form = useForm({
    id: props.result.id,
    items: props.result.items,
    _method: 'PUT',
});



function update() {
    form.post(route(routeUpdate, form.id), {
        onSuccess: (response) => {
            UIkit.notification({message: 'U<PERSON><PERSON>eno', status: 'success'});
        },
        onError: (errors) => {
            let message = '<p class="uk-text-small">Během ukládání došlo k jedné, nebo více chybám. </p>';
            Object.values(errors).forEach(value => {
                message += `<div><p>${value}</p></div>`;
            });

            UIkit.notification({message: message, status: 'danger'});
        }
    });
}

function destroy() {

    UIkit.modal.confirm(textDeleteConfirm).then(function () {
        form.delete(route(routeDestroy, form), {
            onSuccess: (response) => {
                UIkit.notification({message: 'Smazáno', status: 'success'});
            }
        });
    });

}



function addItem(item) {
    item.push(Object.assign({}, newDateObj))
    return null;
}

function removeItem(item, index) {
    item.splice(index, 1);
}


</script>
<template>
    <app-layout :title="result.name">
        <template #header>
            <div
                class="uk-child-width-expand@s uk-margin-small-top uk-flex-middle"
                uk-grid
            >
                <div class="uk-width-expand">
                    <div
                        class="uk-child-width-expand@s uk-flex uk-flex-middle"
                        uk-grid
                    >
                        <div class="uk-width-expand">
                            <div v-if="form.id">
                                Naposledy uloženo
                                <date-time :date="result.updated_at"/>
                                uživatelem {{ result.editor.name }}
                            </div>
                        </div>
                    </div>
                </div>
                <div class="uk-width-auto">
                    <button
                        class="uk-button uk-button-default uk-margin-right"
                        :loading="false"
                        type="submit"
                        @click.prevent="update()"
                    >
                        Uložit
                    </button>
                </div>
            </div>
        </template>
        <form
            class="uk-form-stacked"
            @submit.prevent="update"
        >

            <div class="uk-margin">

                <div class="uk-form-controls">




                    <div
                        class="uk-margin-small"
                        v-for="(item, index) in form.items"
                        v-bind="{item}"
                        :key="index">

                        <div class="uk-grid uk-child-width-expand@s uk-margin-bottom" uk-grid>
                            <div class="uk-width-auto">
                                <input class="uk-input" type="text" placeholder="Číslo" v-model="item.number" style="width: 100px;">
                            </div>
                            <div class="uk-width-expand">
                                <input class="uk-input uk-width-expand" type="text" placeholder="Název" v-model="item.name">
                                <textarea class="uk-textarea" placeholder="Popis" v-model="item.description" rows="2"></textarea>
                            </div>
                            <div class="uk-width-auto">
                                <input class="uk-input" type="text" placeholder="Cena" v-model="item.price" style="width: 100px;">
                            </div>
                            <div class="uk-width-auto">
                                <a class="uk-button uk-button-danger uk-button-small" @click="removeItem(form.items, index)">
                                    <span uk-icon="trash"></span>
                                </a>
                            </div>

                        </div>


                    </div>

                    <div>
                        <a class="uk-button uk-button-small uk-button-primary" @click="addItem(form.items)"><span
                            uk-icon="icon: plus;"></span> Přidat jídlo</a>
                    </div>

                </div>
            </div>

            <div class="">
                <ActionMessage
                    :on="form.recentlySuccessful"
                >
                    Uspěšně uloženo
                </ActionMessage>
                <button
                    class="uk-button uk-button-primary uk-button-large"
                    @click.prevent="update()"
                    :loading="false"
                    type="submit"
                >
                    Uložit
                </button>
            </div>
        </form>
    </app-layout>
</template>
